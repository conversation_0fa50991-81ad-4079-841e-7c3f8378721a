/**
 * 面板插件管理器
 * 负责面板插件的注册、加载、管理等功能
 */
import React from 'react';

export interface PanelPluginManifest {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  icon?: React.ReactNode;
  category: string;
  tags: string[];
  dependencies?: string[];
  permissions?: string[];
  minEditorVersion?: string;
  maxEditorVersion?: string;
}

export interface PanelPluginConfig {
  enabled: boolean;
  settings: Record<string, any>;
  position?: 'left' | 'right' | 'bottom' | 'center';
  size?: number;
  closable?: boolean;
  resizable?: boolean;
}

export interface PanelPlugin {
  manifest: PanelPluginManifest;
  component: React.ComponentType<any>;
  config: PanelPluginConfig;
  onActivate?: () => void;
  onDeactivate?: () => void;
  onSettingsChange?: (settings: Record<string, any>) => void;
}

export interface PluginAPI {
  // 编辑器API
  getSelectedEntity: () => any;
  getScene: () => any;
  addEntity: (entity: any) => void;
  removeEntity: (id: string) => void;
  
  // 面板API
  openPanel: (panelId: string) => void;
  closePanel: (panelId: string) => void;
  showNotification: (message: string, type?: 'success' | 'info' | 'warning' | 'error') => void;
  
  // 事件API
  on: (event: string, callback: Function) => void;
  off: (event: string, callback: Function) => void;
  emit: (event: string, data?: any) => void;
}

class PanelPluginManager {
  private static instance: PanelPluginManager;
  private plugins: Map<string, PanelPlugin> = new Map();
  private pluginConfigs: Map<string, PanelPluginConfig> = new Map();
  private eventListeners: Map<string, Function[]> = new Map();
  private readonly STORAGE_KEY = 'dl-editor-panel-plugins';

  private constructor() {
    this.loadPluginConfigs();
  }

  public static getInstance(): PanelPluginManager {
    if (!PanelPluginManager.instance) {
      PanelPluginManager.instance = new PanelPluginManager();
    }
    return PanelPluginManager.instance;
  }

  /**
   * 注册插件
   */
  public registerPlugin(plugin: PanelPlugin): boolean {
    try {
      // 验证插件
      if (!this.validatePlugin(plugin)) {
        console.error(`Invalid plugin: ${plugin.manifest.id}`);
        return false;
      }

      // 检查依赖
      if (!this.checkDependencies(plugin.manifest.dependencies || [])) {
        console.error(`Plugin dependencies not met: ${plugin.manifest.id}`);
        return false;
      }

      // 检查版本兼容性
      if (!this.checkVersionCompatibility(plugin.manifest)) {
        console.error(`Plugin version incompatible: ${plugin.manifest.id}`);
        return false;
      }

      // 注册插件
      this.plugins.set(plugin.manifest.id, plugin);

      // 加载配置
      const savedConfig = this.pluginConfigs.get(plugin.manifest.id);
      if (savedConfig) {
        plugin.config = { ...plugin.config, ...savedConfig };
      }

      // 如果插件启用，激活它
      if (plugin.config.enabled) {
        this.activatePlugin(plugin.manifest.id);
      }

      console.log(`Plugin registered: ${plugin.manifest.id}`);
      return true;
    } catch (error) {
      console.error(`Failed to register plugin ${plugin.manifest.id}:`, error);
      return false;
    }
  }

  /**
   * 注销插件
   */
  public unregisterPlugin(pluginId: string): boolean {
    const plugin = this.plugins.get(pluginId);
    if (plugin) {
      // 先停用插件
      this.deactivatePlugin(pluginId);
      
      // 移除插件
      this.plugins.delete(pluginId);
      
      console.log(`Plugin unregistered: ${pluginId}`);
      return true;
    }
    return false;
  }

  /**
   * 激活插件
   */
  public activatePlugin(pluginId: string): boolean {
    const plugin = this.plugins.get(pluginId);
    if (plugin && !plugin.config.enabled) {
      try {
        plugin.config.enabled = true;
        plugin.onActivate?.();
        
        this.savePluginConfig(pluginId, plugin.config);
        this.emit('pluginActivated', { pluginId, plugin });
        
        console.log(`Plugin activated: ${pluginId}`);
        return true;
      } catch (error) {
        console.error(`Failed to activate plugin ${pluginId}:`, error);
        plugin.config.enabled = false;
      }
    }
    return false;
  }

  /**
   * 停用插件
   */
  public deactivatePlugin(pluginId: string): boolean {
    const plugin = this.plugins.get(pluginId);
    if (plugin && plugin.config.enabled) {
      try {
        plugin.config.enabled = false;
        plugin.onDeactivate?.();
        
        this.savePluginConfig(pluginId, plugin.config);
        this.emit('pluginDeactivated', { pluginId, plugin });
        
        console.log(`Plugin deactivated: ${pluginId}`);
        return true;
      } catch (error) {
        console.error(`Failed to deactivate plugin ${pluginId}:`, error);
      }
    }
    return false;
  }

  /**
   * 获取所有插件
   */
  public getAllPlugins(): PanelPlugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * 获取已激活的插件
   */
  public getActivePlugins(): PanelPlugin[] {
    return Array.from(this.plugins.values()).filter(plugin => plugin.config.enabled);
  }

  /**
   * 获取插件
   */
  public getPlugin(pluginId: string): PanelPlugin | null {
    return this.plugins.get(pluginId) || null;
  }

  /**
   * 更新插件配置
   */
  public updatePluginConfig(pluginId: string, config: Partial<PanelPluginConfig>): boolean {
    const plugin = this.plugins.get(pluginId);
    if (plugin) {
      plugin.config = { ...plugin.config, ...config };
      this.savePluginConfig(pluginId, plugin.config);
      
      // 通知插件配置变化
      plugin.onSettingsChange?.(plugin.config.settings);
      
      return true;
    }
    return false;
  }

  /**
   * 获取插件API
   */
  public getPluginAPI(): PluginAPI {
    return {
      // 编辑器API
      getSelectedEntity: () => {
        // 实现获取选中实体的逻辑
        return null;
      },
      getScene: () => {
        // 实现获取场景的逻辑
        return null;
      },
      addEntity: (entity: any) => {
        // 实现添加实体的逻辑
      },
      removeEntity: (id: string) => {
        // 实现移除实体的逻辑
      },

      // 面板API
      openPanel: (panelId: string) => {
        this.emit('openPanel', { panelId });
      },
      closePanel: (panelId: string) => {
        this.emit('closePanel', { panelId });
      },
      showNotification: (message: string, type = 'info') => {
        this.emit('showNotification', { message, type });
      },

      // 事件API
      on: (event: string, callback: Function) => {
        this.on(event, callback);
      },
      off: (event: string, callback: Function) => {
        this.off(event, callback);
      },
      emit: (event: string, data?: any) => {
        this.emit(event, data);
      }
    };
  }

  /**
   * 创建插件面板组件
   */
  public createPluginPanel(pluginId: string, props?: any): React.ReactElement | null {
    const plugin = this.plugins.get(pluginId);
    if (plugin && plugin.config.enabled) {
      const PluginComponent = plugin.component;
      return React.createElement(PluginComponent, {
        ...props,
        pluginAPI: this.getPluginAPI(),
        pluginConfig: plugin.config
      });
    }
    return null;
  }

  /**
   * 事件监听
   */
  public on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * 移除事件监听
   */
  public off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   */
  public emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * 验证插件
   */
  private validatePlugin(plugin: PanelPlugin): boolean {
    const { manifest, component } = plugin;
    
    return !!(
      manifest &&
      manifest.id &&
      manifest.name &&
      manifest.version &&
      component &&
      typeof component === 'function'
    );
  }

  /**
   * 检查依赖
   */
  private checkDependencies(dependencies: string[]): boolean {
    return dependencies.every(dep => this.plugins.has(dep));
  }

  /**
   * 检查版本兼容性
   */
  private checkVersionCompatibility(manifest: PanelPluginManifest): boolean {
    // 这里可以实现版本兼容性检查逻辑
    // 暂时返回true
    return true;
  }

  /**
   * 保存插件配置
   */
  private savePluginConfig(pluginId: string, config: PanelPluginConfig): void {
    this.pluginConfigs.set(pluginId, config);
    this.savePluginConfigs();
  }

  /**
   * 加载插件配置
   */
  private loadPluginConfigs(): void {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      if (data) {
        const configs = JSON.parse(data);
        this.pluginConfigs = new Map(Object.entries(configs));
      }
    } catch (error) {
      console.error('Failed to load plugin configs:', error);
    }
  }

  /**
   * 保存插件配置到存储
   */
  private savePluginConfigs(): void {
    try {
      const configs = Object.fromEntries(this.pluginConfigs);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs));
    } catch (error) {
      console.error('Failed to save plugin configs:', error);
    }
  }
}

export default PanelPluginManager;
