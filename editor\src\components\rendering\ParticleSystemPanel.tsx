/**
 * 粒子系统面板组件
 * 提供GPU粒子系统编辑和管理功能
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Slider,
  Switch,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Select,
  List,
  Tag,
  Modal,
  Form,
  Divider,
  Tooltip,
  Badge,
  Progress,
  Collapse,
  ColorPicker,
  InputNumber,
  Upload,
  Alert,
  Statistic
} from 'antd';
import {
  ThunderboltOutlined,
  FireOutlined,
  CloudOutlined,
  StarOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  SettingOutlined,
  EyeOutlined,
  ReloadOutlined,
  ExperimentOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import ParticleSystemService, {
  ParticleSystemDefinition,
  ParticleSystemType,
  EmitterShape,
  BlendMode,
  SortMode,
  ParticlePreset,
  ParticleProperties
} from '../../services/ParticleSystemService';
import './ParticleSystemPanel.less';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;

interface ParticleSystemPanelProps {
  visible: boolean;
  onClose: () => void;
}

const ParticleSystemPanel: React.FC<ParticleSystemPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('systems');
  const [particleSystems, setParticleSystems] = useState<ParticleSystemDefinition[]>([]);
  const [presets, setPresets] = useState<ParticlePreset[]>([]);
  const [selectedSystem, setSelectedSystem] = useState<ParticleSystemDefinition | null>(null);
  const [activeSystem, setActiveSystem] = useState<ParticleSystemDefinition | null>(null);
  const [isSimulating, setIsSimulating] = useState(false);
  const [performanceStats, setPerformanceStats] = useState<any>({});
  const [newSystemModalVisible, setNewSystemModalVisible] = useState(false);
  const [presetModalVisible, setPresetModalVisible] = useState(false);

  const particleService = ParticleSystemService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    particleService.on('particleSystemCreated', handleSystemCreated);
    particleService.on('particleSystemUpdated', handleSystemUpdated);
    particleService.on('simulationStarted', () => setIsSimulating(true));
    particleService.on('simulationStopped', () => setIsSimulating(false));
    particleService.on('presetApplied', handlePresetApplied);
  };

  const cleanupEventListeners = () => {
    particleService.off('particleSystemCreated', handleSystemCreated);
    particleService.off('particleSystemUpdated', handleSystemUpdated);
  };

  const loadData = () => {
    setParticleSystems(particleService.getAllParticleSystems());
    setPresets(particleService.getAllPresets());
    setActiveSystem(particleService.getActiveSystem());
    setIsSimulating(particleService.isSimulationRunning());
    setPerformanceStats(particleService.getPerformanceStats());
  };

  const handleSystemCreated = (system: ParticleSystemDefinition) => {
    setParticleSystems(prev => [...prev, system]);
    setPerformanceStats(particleService.getPerformanceStats());
  };

  const handleSystemUpdated = (system: ParticleSystemDefinition) => {
    setParticleSystems(prev => prev.map(s => s.id === system.id ? system : s));
    if (selectedSystem?.id === system.id) {
      setSelectedSystem(system);
    }
    setPerformanceStats(particleService.getPerformanceStats());
  };

  const handlePresetApplied = ({ system }: any) => {
    if (selectedSystem?.id === system.id) {
      setSelectedSystem(system);
      form.setFieldsValue(system.properties);
    }
  };

  const handleCreateNewSystem = async (values: any) => {
    const properties: ParticleProperties = {
      name: values.name,
      type: values.type,
      enabled: true,
      emitterShape: EmitterShape.POINT,
      emitterSize: [1, 1, 1],
      emitterPosition: [0, 0, 0],
      emitterRotation: [0, 0, 0],
      maxParticles: values.maxParticles || 1000,
      emissionRate: values.emissionRate || 50,
      burstCount: 0,
      burstInterval: 0,
      lifetime: { min: 1, max: 3 },
      velocity: {
        initial: [0, 5, 0],
        randomness: [1, 1, 1]
      },
      size: {
        initial: 1,
        curve: [1, 1, 1, 0],
        randomness: 0.2
      },
      color: {
        initial: [1, 1, 1, 1],
        curve: [[1, 1, 1, 1], [1, 1, 1, 0.5], [1, 1, 1, 0.2], [1, 1, 1, 0]],
        randomness: 0.1
      },
      rotation: {
        initial: 0,
        velocity: 0,
        randomness: 0
      },
      gravity: [0, -9.8, 0],
      drag: 0.1,
      mass: 1,
      material: {
        blendMode: BlendMode.ADDITIVE,
        sortMode: SortMode.DISTANCE,
        billboard: true,
        softParticles: false
      },
      collision: {
        enabled: false,
        bounce: 0.5,
        friction: 0.1,
        killOnCollision: false
      },
      noise: {
        enabled: false,
        strength: 1,
        frequency: 0.1,
        octaves: 1
      },
      customProperties: {}
    };

    const system = particleService.createParticleSystem(properties);
    setSelectedSystem(system);
    setNewSystemModalVisible(false);
    form.resetFields();
  };

  const handlePropertyChange = (property: string, value: any) => {
    if (!selectedSystem) return;

    const updates = { [property]: value };
    particleService.updateParticleSystem(selectedSystem.id, updates);
  };

  const handleStartSimulation = async () => {
    if (!selectedSystem) return;

    try {
      await particleService.startSimulation(selectedSystem.id);
      setActiveSystem(selectedSystem);
    } catch (error) {
      console.error('Failed to start simulation:', error);
    }
  };

  const handleStopSimulation = () => {
    particleService.stopSimulation();
    setActiveSystem(null);
  };

  const handleApplyPreset = (presetId: string) => {
    if (!selectedSystem) return;

    particleService.applyPreset(selectedSystem.id, presetId);
    setPresetModalVisible(false);
  };

  const handleDeleteSystem = (systemId: string) => {
    Modal.confirm({
      title: t('particle.confirmDelete'),
      content: t('particle.confirmDeleteContent'),
      onOk: () => {
        particleService.deleteParticleSystem(systemId);
        if (selectedSystem?.id === systemId) {
          setSelectedSystem(null);
        }
        loadData();
      }
    });
  };

  // 渲染粒子系统列表
  const renderSystemsList = () => (
    <div className="systems-list">
      <div className="systems-header">
        <Space>
          <Button type="primary" onClick={() => setNewSystemModalVisible(true)}>
            <ThunderboltOutlined />
            {t('particle.newSystem')}
          </Button>
          <Button onClick={() => setPresetModalVisible(true)}>
            <StarOutlined />
            {t('particle.presets')}
          </Button>
        </Space>
      </div>

      <List
        dataSource={particleSystems}
        renderItem={(system) => (
          <List.Item
            className={`system-item ${selectedSystem?.id === system.id ? 'selected' : ''} ${
              activeSystem?.id === system.id ? 'active' : ''
            }`}
            onClick={() => {
              setSelectedSystem(system);
              form.setFieldsValue(system.properties);
            }}
            actions={[
              <Tooltip title={t('particle.simulate')}>
                <Button
                  type="text"
                  icon={activeSystem?.id === system.id ? <StopOutlined /> : <PlayCircleOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (activeSystem?.id === system.id) {
                      handleStopSimulation();
                    } else {
                      setSelectedSystem(system);
                      handleStartSimulation();
                    }
                  }}
                />
              </Tooltip>,
              <Tooltip title={t('particle.clone')}>
                <Button
                  type="text"
                  icon={<CopyOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    particleService.cloneParticleSystem(system.id);
                    loadData();
                  }}
                />
              </Tooltip>,
              <Tooltip title={t('particle.delete')}>
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  danger
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteSystem(system.id);
                  }}
                />
              </Tooltip>
            ]}
          >
            <List.Item.Meta
              avatar={
                <div className={`system-icon ${system.properties.type}`}>
                  {system.properties.type === ParticleSystemType.FIRE && <FireOutlined />}
                  {system.properties.type === ParticleSystemType.SMOKE && <CloudOutlined />}
                  {system.properties.type === ParticleSystemType.EXPLOSION && <ThunderboltOutlined />}
                  {!['fire', 'smoke', 'explosion'].includes(system.properties.type) && <StarOutlined />}
                </div>
              }
              title={
                <div>
                  <Text strong>{system.properties.name}</Text>
                  {system.properties.enabled && (
                    <Tag color="green" size="small" style={{ marginLeft: 8 }}>
                      {t('particle.enabled')}
                    </Tag>
                  )}
                  {activeSystem?.id === system.id && (
                    <Tag color="blue" size="small" style={{ marginLeft: 4 }}>
                      {t('particle.simulating')}
                    </Tag>
                  )}
                </div>
              }
              description={
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {system.properties.type} • {system.properties.maxParticles} particles
                  </Text>
                  <br />
                  <Space style={{ marginTop: 4 }}>
                    <Progress
                      percent={system.metadata.performance}
                      size="small"
                      strokeColor="#52c41a"
                      showInfo={false}
                      style={{ width: 60 }}
                    />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      Performance: {system.metadata.performance}%
                    </Text>
                  </Space>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );

  // 渲染属性编辑器
  const renderPropertyEditor = () => {
    if (!selectedSystem) {
      return (
        <div className="no-system-selected">
          <Text type="secondary">{t('particle.selectSystemToEdit')}</Text>
        </div>
      );
    }

    return (
      <div className="property-editor">
        <div className="editor-header">
          <Title level={4}>{selectedSystem.properties.name}</Title>
          <Space>
            <Tag color="blue">{selectedSystem.properties.type}</Tag>
            <Switch
              checked={selectedSystem.properties.enabled}
              onChange={(enabled) => handlePropertyChange('enabled', enabled)}
            />
          </Space>
        </div>

        <Divider />

        <Collapse defaultActiveKey={['emission', 'appearance']}>
          <Panel header={t('particle.emission')} key="emission">
            <Form layout="vertical">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item label={t('particle.maxParticles')}>
                    <Slider
                      min={100}
                      max={10000}
                      step={100}
                      value={selectedSystem.properties.maxParticles}
                      onChange={(value) => handlePropertyChange('maxParticles', value)}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label={t('particle.emissionRate')}>
                    <Slider
                      min={1}
                      max={500}
                      step={1}
                      value={selectedSystem.properties.emissionRate}
                      onChange={(value) => handlePropertyChange('emissionRate', value)}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label={t('particle.emitterShape')}>
                <Select
                  value={selectedSystem.properties.emitterShape}
                  onChange={(value) => handlePropertyChange('emitterShape', value)}
                >
                  {Object.values(EmitterShape).map(shape => (
                    <Option key={shape} value={shape}>{shape.toUpperCase()}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item label={t('particle.lifetimeMin')}>
                    <Slider
                      min={0.1}
                      max={10}
                      step={0.1}
                      value={selectedSystem.properties.lifetime.min}
                      onChange={(value) => {
                        const lifetime = { ...selectedSystem.properties.lifetime, min: value };
                        handlePropertyChange('lifetime', lifetime);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label={t('particle.lifetimeMax')}>
                    <Slider
                      min={0.1}
                      max={10}
                      step={0.1}
                      value={selectedSystem.properties.lifetime.max}
                      onChange={(value) => {
                        const lifetime = { ...selectedSystem.properties.lifetime, max: value };
                        handlePropertyChange('lifetime', lifetime);
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Panel>

          <Panel header={t('particle.appearance')} key="appearance">
            <Form layout="vertical">
              <Form.Item label={t('particle.initialSize')}>
                <Slider
                  min={0.1}
                  max={5}
                  step={0.1}
                  value={selectedSystem.properties.size.initial}
                  onChange={(value) => {
                    const size = { ...selectedSystem.properties.size, initial: value };
                    handlePropertyChange('size', size);
                  }}
                />
              </Form.Item>

              <Form.Item label={t('particle.initialColor')}>
                <ColorPicker
                  value={selectedSystem.properties.color.initial}
                  onChange={(color) => {
                    const colorValue = { 
                      ...selectedSystem.properties.color, 
                      initial: [color.r/255, color.g/255, color.b/255, color.a || 1] 
                    };
                    handlePropertyChange('color', colorValue);
                  }}
                />
              </Form.Item>

              <Form.Item label={t('particle.blendMode')}>
                <Select
                  value={selectedSystem.properties.material.blendMode}
                  onChange={(value) => {
                    const material = { ...selectedSystem.properties.material, blendMode: value };
                    handlePropertyChange('material', material);
                  }}
                >
                  {Object.values(BlendMode).map(mode => (
                    <Option key={mode} value={mode}>{mode.toUpperCase()}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
          </Panel>

          <Panel header={t('particle.physics')} key="physics">
            <Form layout="vertical">
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Form.Item label={t('particle.gravityX')}>
                    <Slider
                      min={-20}
                      max={20}
                      step={0.1}
                      value={selectedSystem.properties.gravity[0]}
                      onChange={(value) => {
                        const gravity = [...selectedSystem.properties.gravity] as [number, number, number];
                        gravity[0] = value;
                        handlePropertyChange('gravity', gravity);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label={t('particle.gravityY')}>
                    <Slider
                      min={-20}
                      max={20}
                      step={0.1}
                      value={selectedSystem.properties.gravity[1]}
                      onChange={(value) => {
                        const gravity = [...selectedSystem.properties.gravity] as [number, number, number];
                        gravity[1] = value;
                        handlePropertyChange('gravity', gravity);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label={t('particle.gravityZ')}>
                    <Slider
                      min={-20}
                      max={20}
                      step={0.1}
                      value={selectedSystem.properties.gravity[2]}
                      onChange={(value) => {
                        const gravity = [...selectedSystem.properties.gravity] as [number, number, number];
                        gravity[2] = value;
                        handlePropertyChange('gravity', gravity);
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label={t('particle.drag')}>
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  value={selectedSystem.properties.drag}
                  onChange={(value) => handlePropertyChange('drag', value)}
                />
              </Form.Item>

              <Form.Item label={t('particle.collision')}>
                <Switch
                  checked={selectedSystem.properties.collision.enabled}
                  onChange={(enabled) => {
                    const collision = { ...selectedSystem.properties.collision, enabled };
                    handlePropertyChange('collision', collision);
                  }}
                />
              </Form.Item>
            </Form>
          </Panel>
        </Collapse>
      </div>
    );
  };

  // 渲染预览和统计
  const renderPreviewAndStats = () => (
    <div className="preview-and-stats">
      <Card title={t('particle.preview')} size="small">
        <div className="preview-viewport">
          {/* 3D预览区域 */}
          <div style={{
            width: '100%',
            height: '200px',
            background: 'linear-gradient(45deg, #000 25%, transparent 25%), linear-gradient(-45deg, #000 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #000 75%), linear-gradient(-45deg, transparent 75%, #000 75%)',
            backgroundSize: '20px 20px',
            backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            position: 'relative'
          }}>
            <Text type="secondary">Particle Preview</Text>
            {isSimulating && (
              <div className="simulation-indicator">
                <ThunderboltOutlined style={{ color: '#1890ff', fontSize: '24px' }} />
              </div>
            )}
          </div>
        </div>

        <div className="preview-controls" style={{ marginTop: 12 }}>
          <Space>
            <Button
              type={isSimulating ? 'default' : 'primary'}
              icon={isSimulating ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={isSimulating ? handleStopSimulation : handleStartSimulation}
              disabled={!selectedSystem}
            >
              {isSimulating ? t('particle.pause') : t('particle.play')}
            </Button>
            <Button icon={<StopOutlined />} onClick={handleStopSimulation} disabled={!isSimulating}>
              {t('particle.stop')}
            </Button>
            <Button icon={<ReloadOutlined />}>
              {t('particle.reset')}
            </Button>
          </Space>
        </div>
      </Card>

      <Card title={t('particle.statistics')} size="small" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title={t('particle.totalSystems')}
              value={performanceStats.totalSystems || 0}
              prefix={<ThunderboltOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('particle.activeSystems')}
              value={performanceStats.activeSystems || 0}
              prefix={<PlayCircleOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('particle.totalParticles')}
              value={performanceStats.totalParticles || 0}
              prefix={<StarOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title={t('particle.memoryUsage')}
              value={performanceStats.memoryUsage || 0}
              suffix="MB"
              precision={1}
            />
          </Col>
        </Row>

        <Divider />

        <div className="performance-indicator">
          <Text type="secondary">{t('particle.performance')}:</Text>
          <Progress
            percent={performanceStats.averagePerformance || 100}
            strokeColor={
              (performanceStats.averagePerformance || 100) > 70 ? '#52c41a' :
              (performanceStats.averagePerformance || 100) > 40 ? '#faad14' : '#ff4d4f'
            }
            style={{ marginTop: 8 }}
          />
        </div>
      </Card>
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <ThunderboltOutlined />
          {t('particle.particleSystem')}
          {isSimulating && (
            <Badge status="processing" text={t('particle.simulating')} />
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1400}
      footer={[
        <Button key="export" icon={<DownloadOutlined />}>
          {t('particle.export')}
        </Button>,
        <Button key="import" icon={<UploadOutlined />}>
          {t('particle.import')}
        </Button>,
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="particle-system-panel"
    >
      <Row gutter={[16, 0]} style={{ height: '70vh' }}>
        <Col span={8}>
          {renderSystemsList()}
        </Col>
        <Col span={10}>
          {renderPropertyEditor()}
        </Col>
        <Col span={6}>
          {renderPreviewAndStats()}
        </Col>
      </Row>

      {/* 新建系统对话框 */}
      <Modal
        title={t('particle.createNewSystem')}
        open={newSystemModalVisible}
        onCancel={() => setNewSystemModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateNewSystem}>
          <Form.Item
            name="name"
            label={t('particle.systemName')}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label={t('particle.systemType')}
            rules={[{ required: true }]}
          >
            <Select>
              {Object.values(ParticleSystemType).map(type => (
                <Option key={type} value={type}>{type.replace('_', ' ').toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="maxParticles" label={t('particle.maxParticles')}>
            <Slider min={100} max={5000} step={100} defaultValue={1000} />
          </Form.Item>
          <Form.Item name="emissionRate" label={t('particle.emissionRate')}>
            <Slider min={1} max={200} step={1} defaultValue={50} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 预设选择对话框 */}
      <Modal
        title={t('particle.selectPreset')}
        open={presetModalVisible}
        onCancel={() => setPresetModalVisible(false)}
        footer={null}
        width={800}
      >
        <Row gutter={[16, 16]}>
          {presets.map(preset => (
            <Col span={8} key={preset.id}>
              <Card
                hoverable
                cover={<img src={preset.thumbnail} alt={preset.name} style={{ height: '120px', objectFit: 'cover' }} />}
                onClick={() => handleApplyPreset(preset.id)}
              >
                <Card.Meta
                  title={preset.name}
                  description={
                    <div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{preset.description}</Text>
                      <br />
                      <Space wrap style={{ marginTop: 4 }}>
                        {preset.tags.map(tag => (
                          <Tag key={tag} size="small">{tag}</Tag>
                        ))}
                      </Space>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Modal>
    </Modal>
  );
};

export default ParticleSystemPanel;
