/**
 * 高级光照面板组件
 * 提供光照编辑和管理功能
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Slider,
  Switch,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Select,
  List,
  Tag,
  Modal,
  Form,
  Divider,
  Tooltip,
  Badge,
  Progress,
  Collapse,
  ColorPicker,
  InputNumber,
  Tree,
  Upload,
  Alert
} from 'antd';
import {
  BulbOutlined,
  SunOutlined,
  EyeOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  DownloadOutlined,
  UploadOutlined,
  ThunderboltOutlined,
  EnvironmentOutlined,
  RadarChartOutlined,
  FireOutlined,
  CloudOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import AdvancedLightingService, {
  LightDefinition,
  LightingScene,
  LightType,
  ShadowType,
  GlobalIlluminationType,
  LightProbe,
  IBLEnvironment,
  LightProperties
} from '../../services/AdvancedLightingService';
import './LightingPanel.less';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;
const { TreeNode } = Tree;

interface LightingPanelProps {
  visible: boolean;
  onClose: () => void;
}

const LightingPanel: React.FC<LightingPanelProps> = ({
  visible,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('lights');
  const [lightingScenes, setLightingScenes] = useState<LightingScene[]>([]);
  const [activeLightingScene, setActiveLightingScene] = useState<LightingScene | null>(null);
  const [lights, setLights] = useState<LightDefinition[]>([]);
  const [lightProbes, setLightProbes] = useState<LightProbe[]>([]);
  const [iblEnvironments, setIblEnvironments] = useState<IBLEnvironment[]>([]);
  const [selectedLight, setSelectedLight] = useState<LightDefinition | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [newLightModalVisible, setNewLightModalVisible] = useState(false);
  const [iblModalVisible, setIblModalVisible] = useState(false);

  const lightingService = AdvancedLightingService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible]);

  const setupEventListeners = () => {
    lightingService.on('lightCreated', handleLightCreated);
    lightingService.on('lightUpdated', handleLightUpdated);
    lightingService.on('lightingSceneCreated', handleLightingSceneCreated);
    lightingService.on('activeLightingSceneChanged', handleActiveLightingSceneChanged);
    lightingService.on('lightmapBakingStarted', () => setIsUpdating(true));
    lightingService.on('lightmapBakingCompleted', () => setIsUpdating(false));
  };

  const cleanupEventListeners = () => {
    lightingService.off('lightCreated', handleLightCreated);
    lightingService.off('lightUpdated', handleLightUpdated);
    lightingService.off('lightingSceneCreated', handleLightingSceneCreated);
    lightingService.off('activeLightingSceneChanged', handleActiveLightingSceneChanged);
  };

  const loadData = () => {
    setLightingScenes(lightingService.getAllLightingScenes());
    setLights(lightingService.getAllLights());
    setLightProbes(lightingService.getAllLightProbes());
    setIblEnvironments(lightingService.getAllIBLEnvironments());
    setActiveLightingScene(lightingService.getActiveLightingScene());
    setIsUpdating(lightingService.isUpdatingLighting());
  };

  const handleLightCreated = (light: LightDefinition) => {
    setLights(prev => [...prev, light]);
  };

  const handleLightUpdated = (light: LightDefinition) => {
    setLights(prev => prev.map(l => l.id === light.id ? light : l));
    if (selectedLight?.id === light.id) {
      setSelectedLight(light);
    }
  };

  const handleLightingSceneCreated = (scene: LightingScene) => {
    setLightingScenes(prev => [...prev, scene]);
  };

  const handleActiveLightingSceneChanged = (scene: LightingScene) => {
    setActiveLightingScene(scene);
  };

  const handleCreateNewLight = async (values: any) => {
    const properties: LightProperties = {
      name: values.name,
      type: values.type,
      enabled: true,
      position: [0, 5, 0],
      direction: [0, -1, 0],
      color: [1, 1, 1],
      intensity: values.intensity || 1,
      range: values.range || 10,
      decay: 2,
      castShadow: values.castShadow || false,
      shadowType: values.shadowType || ShadowType.BASIC,
      shadowMapSize: 1024,
      shadowBias: 0.0001,
      shadowNormalBias: 0.01,
      shadowRadius: 1,
      shadowBlurSamples: 16,
      customProperties: {}
    };

    const light = lightingService.createLight(properties);
    setSelectedLight(light);
    setNewLightModalVisible(false);
    form.resetFields();
  };

  const handleLightPropertyChange = (property: string, value: any) => {
    if (!selectedLight) return;

    lightingService.updateLight(selectedLight.id, { [property]: value });
  };

  const handleDeleteLight = (lightId: string) => {
    Modal.confirm({
      title: t('lighting.confirmDeleteLight'),
      content: t('lighting.confirmDeleteLightContent'),
      onOk: () => {
        lightingService.deleteLight(lightId);
        if (selectedLight?.id === lightId) {
          setSelectedLight(null);
        }
        loadData();
      }
    });
  };

  const handleBakeLightmaps = async () => {
    if (!activeLightingScene) return;

    try {
      // 模拟获取场景对象
      const objects = ['object1', 'object2', 'object3'];
      await lightingService.bakeLightmaps(objects);
    } catch (error) {
      console.error('Failed to bake lightmaps:', error);
    }
  };

  const handleAutoPlaceProbes = async () => {
    if (!activeLightingScene) return;

    try {
      const bounds = {
        min: [-10, 0, -10] as [number, number, number],
        max: [10, 10, 10] as [number, number, number]
      };
      await lightingService.autoPlaceLightProbes(bounds);
      loadData();
    } catch (error) {
      console.error('Failed to auto place probes:', error);
    }
  };

  // 渲染光源列表
  const renderLightsList = () => (
    <div className="lights-list">
      <div className="lights-header">
        <Space>
          <Button type="primary" onClick={() => setNewLightModalVisible(true)}>
            <PlusOutlined />
            {t('lighting.newLight')}
          </Button>
          <Button onClick={() => setIblModalVisible(true)}>
            <EnvironmentOutlined />
            {t('lighting.iblEnvironment')}
          </Button>
        </Space>
      </div>

      <Tree
        showLine
        showIcon
        defaultExpandAll
        onSelect={(selectedKeys) => {
          if (selectedKeys.length > 0) {
            const lightId = selectedKeys[0] as string;
            const light = lights.find(l => l.id === lightId);
            if (light) {
              setSelectedLight(light);
            }
          }
        }}
      >
        <TreeNode title="Directional Lights" key="directional" icon={<SunOutlined />}>
          {lights.filter(l => l.properties.type === LightType.DIRECTIONAL).map(light => (
            <TreeNode
              title={
                <div className="light-node">
                  <span>{light.properties.name}</span>
                  <Space size="small">
                    <Switch
                      size="small"
                      checked={light.properties.enabled}
                      onChange={(enabled) => handleLightPropertyChange('enabled', enabled)}
                    />
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteLight(light.id);
                      }}
                    />
                  </Space>
                </div>
              }
              key={light.id}
              icon={<SunOutlined style={{ color: light.properties.enabled ? '#faad14' : '#d9d9d9' }} />}
              isLeaf
            />
          ))}
        </TreeNode>

        <TreeNode title="Point Lights" key="point" icon={<BulbOutlined />}>
          {lights.filter(l => l.properties.type === LightType.POINT).map(light => (
            <TreeNode
              title={
                <div className="light-node">
                  <span>{light.properties.name}</span>
                  <Space size="small">
                    <Switch
                      size="small"
                      checked={light.properties.enabled}
                      onChange={(enabled) => handleLightPropertyChange('enabled', enabled)}
                    />
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteLight(light.id);
                      }}
                    />
                  </Space>
                </div>
              }
              key={light.id}
              icon={<BulbOutlined style={{ color: light.properties.enabled ? '#1890ff' : '#d9d9d9' }} />}
              isLeaf
            />
          ))}
        </TreeNode>

        <TreeNode title="Spot Lights" key="spot" icon={<FireOutlined />}>
          {lights.filter(l => l.properties.type === LightType.SPOT).map(light => (
            <TreeNode
              title={
                <div className="light-node">
                  <span>{light.properties.name}</span>
                  <Space size="small">
                    <Switch
                      size="small"
                      checked={light.properties.enabled}
                      onChange={(enabled) => handleLightPropertyChange('enabled', enabled)}
                    />
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteLight(light.id);
                      }}
                    />
                  </Space>
                </div>
              }
              key={light.id}
              icon={<FireOutlined style={{ color: light.properties.enabled ? '#ff4d4f' : '#d9d9d9' }} />}
              isLeaf
            />
          ))}
        </TreeNode>

        <TreeNode title="Area Lights" key="area" icon={<RadarChartOutlined />}>
          {lights.filter(l => l.properties.type === LightType.AREA).map(light => (
            <TreeNode
              title={
                <div className="light-node">
                  <span>{light.properties.name}</span>
                  <Space size="small">
                    <Switch
                      size="small"
                      checked={light.properties.enabled}
                      onChange={(enabled) => handleLightPropertyChange('enabled', enabled)}
                    />
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteLight(light.id);
                      }}
                    />
                  </Space>
                </div>
              }
              key={light.id}
              icon={<RadarChartOutlined style={{ color: light.properties.enabled ? '#52c41a' : '#d9d9d9' }} />}
              isLeaf
            />
          ))}
        </TreeNode>

        <TreeNode title="Ambient Lights" key="ambient" icon={<CloudOutlined />}>
          {lights.filter(l => l.properties.type === LightType.AMBIENT || l.properties.type === LightType.HEMISPHERE).map(light => (
            <TreeNode
              title={
                <div className="light-node">
                  <span>{light.properties.name}</span>
                  <Space size="small">
                    <Switch
                      size="small"
                      checked={light.properties.enabled}
                      onChange={(enabled) => handleLightPropertyChange('enabled', enabled)}
                    />
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteLight(light.id);
                      }}
                    />
                  </Space>
                </div>
              }
              key={light.id}
              icon={<CloudOutlined style={{ color: light.properties.enabled ? '#722ed1' : '#d9d9d9' }} />}
              isLeaf
            />
          ))}
        </TreeNode>
      </Tree>
    </div>
  );

  // 渲染光源属性编辑器
  const renderLightEditor = () => {
    if (!selectedLight) {
      return (
        <div className="no-light-selected">
          <Text type="secondary">{t('lighting.selectLightToEdit')}</Text>
        </div>
      );
    }

    return (
      <div className="light-editor">
        <div className="editor-header">
          <Title level={4}>{selectedLight.properties.name}</Title>
          <Tag color="blue">{selectedLight.properties.type}</Tag>
        </div>

        <Divider />

        <Form layout="vertical">
          <Form.Item label={t('lighting.enabled')}>
            <Switch
              checked={selectedLight.properties.enabled}
              onChange={(enabled) => handleLightPropertyChange('enabled', enabled)}
            />
          </Form.Item>

          <Form.Item label={t('lighting.color')}>
            <ColorPicker
              value={selectedLight.properties.color}
              onChange={(color) => handleLightPropertyChange('color', [color.r/255, color.g/255, color.b/255])}
            />
          </Form.Item>

          <Form.Item label={t('lighting.intensity')}>
            <Slider
              min={0}
              max={10}
              step={0.1}
              value={selectedLight.properties.intensity}
              onChange={(value) => handleLightPropertyChange('intensity', value)}
            />
          </Form.Item>

          {selectedLight.properties.temperature && (
            <Form.Item label={t('lighting.temperature')}>
              <Slider
                min={1000}
                max={10000}
                step={100}
                value={selectedLight.properties.temperature}
                onChange={(value) => handleLightPropertyChange('temperature', value)}
              />
            </Form.Item>
          )}

          {(selectedLight.properties.type === LightType.POINT || selectedLight.properties.type === LightType.SPOT) && (
            <Form.Item label={t('lighting.range')}>
              <Slider
                min={0.1}
                max={100}
                step={0.1}
                value={selectedLight.properties.range}
                onChange={(value) => handleLightPropertyChange('range', value)}
              />
            </Form.Item>
          )}

          {selectedLight.properties.type === LightType.SPOT && (
            <>
              <Form.Item label={t('lighting.angle')}>
                <Slider
                  min={0}
                  max={180}
                  step={1}
                  value={selectedLight.properties.angle || 45}
                  onChange={(value) => handleLightPropertyChange('angle', value)}
                />
              </Form.Item>
              <Form.Item label={t('lighting.penumbra')}>
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  value={selectedLight.properties.penumbra || 0}
                  onChange={(value) => handleLightPropertyChange('penumbra', value)}
                />
              </Form.Item>
            </>
          )}

          <Divider>{t('lighting.shadows')}</Divider>

          <Form.Item label={t('lighting.castShadow')}>
            <Switch
              checked={selectedLight.properties.castShadow}
              onChange={(castShadow) => handleLightPropertyChange('castShadow', castShadow)}
            />
          </Form.Item>

          {selectedLight.properties.castShadow && (
            <>
              <Form.Item label={t('lighting.shadowType')}>
                <Select
                  value={selectedLight.properties.shadowType}
                  onChange={(value) => handleLightPropertyChange('shadowType', value)}
                >
                  {Object.values(ShadowType).map(type => (
                    <Option key={type} value={type}>{type.toUpperCase()}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label={t('lighting.shadowMapSize')}>
                <Select
                  value={selectedLight.properties.shadowMapSize}
                  onChange={(value) => handleLightPropertyChange('shadowMapSize', value)}
                >
                  <Option value={512}>512</Option>
                  <Option value={1024}>1024</Option>
                  <Option value={2048}>2048</Option>
                  <Option value={4096}>4096</Option>
                </Select>
              </Form.Item>

              <Form.Item label={t('lighting.shadowBias')}>
                <Slider
                  min={0}
                  max={0.01}
                  step={0.0001}
                  value={selectedLight.properties.shadowBias}
                  onChange={(value) => handleLightPropertyChange('shadowBias', value)}
                />
              </Form.Item>
            </>
          )}
        </Form>

        <Divider />

        <div className="light-stats">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Text type="secondary">Performance:</Text>
              <Progress
                percent={selectedLight.metadata.performance}
                size="small"
                strokeColor="#52c41a"
              />
            </Col>
            <Col span={12}>
              <Text type="secondary">Quality:</Text>
              <Progress
                percent={selectedLight.metadata.quality}
                size="small"
                strokeColor="#1890ff"
              />
            </Col>
          </Row>
        </div>
      </div>
    );
  };

  // 渲染全局光照设置
  const renderGlobalIllumination = () => (
    <div className="global-illumination">
      <Card title={t('lighting.globalIllumination')} size="small">
        <Form layout="vertical">
          <Form.Item label={t('lighting.giType')}>
            <Select
              value={activeLightingScene?.globalIllumination.type}
              onChange={(value) => lightingService.updateGlobalIllumination({ type: value })}
            >
              {Object.values(GlobalIlluminationType).map(type => (
                <Option key={type} value={type}>{type.replace('_', ' ').toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label={t('lighting.enabled')}>
            <Switch
              checked={activeLightingScene?.globalIllumination.enabled}
              onChange={(enabled) => lightingService.updateGlobalIllumination({ enabled })}
            />
          </Form.Item>

          {activeLightingScene?.globalIllumination.type === GlobalIlluminationType.LIGHT_PROBES && (
            <>
              <Form.Item label={t('lighting.probeSpacing')}>
                <Slider
                  min={1}
                  max={20}
                  step={0.5}
                  value={activeLightingScene.globalIllumination.probeSpacing}
                  onChange={(value) => lightingService.updateGlobalIllumination({ probeSpacing: value })}
                />
              </Form.Item>

              <Button onClick={handleAutoPlaceProbes} block>
                <ThunderboltOutlined />
                {t('lighting.autoPlaceProbes')}
              </Button>
            </>
          )}

          {activeLightingScene?.globalIllumination.type === GlobalIlluminationType.LIGHTMAPS && (
            <>
              <Form.Item label={t('lighting.lightmapResolution')}>
                <Select
                  value={activeLightingScene.globalIllumination.lightmapResolution}
                  onChange={(value) => lightingService.updateGlobalIllumination({ lightmapResolution: value })}
                >
                  <Option value={256}>256</Option>
                  <Option value={512}>512</Option>
                  <Option value={1024}>1024</Option>
                  <Option value={2048}>2048</Option>
                </Select>
              </Form.Item>

              <Button onClick={handleBakeLightmaps} loading={isUpdating} block>
                <FireOutlined />
                {t('lighting.bakeLightmaps')}
              </Button>
            </>
          )}
        </Form>
      </Card>

      <Card title={t('lighting.ambientOcclusion')} size="small" style={{ marginTop: 16 }}>
        <Form layout="vertical">
          <Form.Item label={t('lighting.enabled')}>
            <Switch
              checked={activeLightingScene?.ambientOcclusion.enabled}
              onChange={(enabled) => lightingService.updateAmbientOcclusion({ enabled })}
            />
          </Form.Item>

          <Form.Item label={t('lighting.aoType')}>
            <Select
              value={activeLightingScene?.ambientOcclusion.type}
              onChange={(value) => lightingService.updateAmbientOcclusion({ type: value })}
            >
              <Option value="ssao">SSAO</Option>
              <Option value="hbao">HBAO</Option>
              <Option value="gtao">GTAO</Option>
              <Option value="raytraced">Ray Traced</Option>
            </Select>
          </Form.Item>

          <Form.Item label={t('lighting.aoRadius')}>
            <Slider
              min={0.1}
              max={5}
              step={0.1}
              value={activeLightingScene?.ambientOcclusion.radius}
              onChange={(value) => lightingService.updateAmbientOcclusion({ radius: value })}
            />
          </Form.Item>

          <Form.Item label={t('lighting.aoIntensity')}>
            <Slider
              min={0}
              max={3}
              step={0.1}
              value={activeLightingScene?.ambientOcclusion.intensity}
              onChange={(value) => lightingService.updateAmbientOcclusion({ intensity: value })}
            />
          </Form.Item>
        </Form>
      </Card>
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <BulbOutlined />
          {t('lighting.advancedLighting')}
          {activeLightingScene && (
            <Badge count={activeLightingScene.lights.filter(l => l.properties.enabled).length} showZero>
              <Tag color="blue">{activeLightingScene.name}</Tag>
            </Badge>
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1400}
      footer={[
        <Button key="optimize" onClick={() => lightingService.optimizeLightingSettings()}>
          <ThunderboltOutlined />
          {t('lighting.optimize')}
        </Button>,
        <Button key="export" icon={<DownloadOutlined />}>
          {t('lighting.export')}
        </Button>,
        <Button key="import" icon={<UploadOutlined />}>
          {t('lighting.import')}
        </Button>,
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>
      ]}
      className="lighting-panel"
    >
      <Row gutter={[16, 0]} style={{ height: '70vh' }}>
        <Col span={8}>
          {renderLightsList()}
        </Col>
        <Col span={8}>
          {renderLightEditor()}
        </Col>
        <Col span={8}>
          {renderGlobalIllumination()}
        </Col>
      </Row>

      {/* 新建光源对话框 */}
      <Modal
        title={t('lighting.createNewLight')}
        open={newLightModalVisible}
        onCancel={() => setNewLightModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateNewLight}>
          <Form.Item
            name="name"
            label={t('lighting.lightName')}
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="type"
            label={t('lighting.lightType')}
            rules={[{ required: true }]}
          >
            <Select>
              {Object.values(LightType).map(type => (
                <Option key={type} value={type}>{type.replace('_', ' ').toUpperCase()}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="intensity" label={t('lighting.intensity')}>
            <Slider min={0} max={10} step={0.1} defaultValue={1} />
          </Form.Item>
          <Form.Item name="castShadow" valuePropName="checked">
            <Switch>{t('lighting.castShadow')}</Switch>
          </Form.Item>
        </Form>
      </Modal>

      {/* IBL环境对话框 */}
      <Modal
        title={t('lighting.iblEnvironments')}
        open={iblModalVisible}
        onCancel={() => setIblModalVisible(false)}
        footer={null}
        width={800}
      >
        <Row gutter={[16, 16]}>
          {iblEnvironments.map(env => (
            <Col span={8} key={env.id}>
              <Card
                hoverable
                cover={<div style={{ height: '120px', background: 'linear-gradient(45deg, #1890ff, #52c41a)' }} />}
                onClick={() => {
                  if (activeLightingScene) {
                    activeLightingScene.iblEnvironment = env;
                    lightingService.setActiveLightingScene(activeLightingScene.id);
                  }
                  setIblModalVisible(false);
                }}
              >
                <Card.Meta
                  title={env.name}
                  description={
                    <div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{env.description}</Text>
                      <br />
                      <Space wrap style={{ marginTop: 4 }}>
                        {env.tags.map(tag => (
                          <Tag key={tag} size="small">{tag}</Tag>
                        ))}
                      </Space>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Modal>
    </Modal>
  );
};

export default LightingPanel;
