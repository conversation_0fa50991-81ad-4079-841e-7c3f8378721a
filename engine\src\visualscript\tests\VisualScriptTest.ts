/**
 * 视觉脚本系统测试
 * 验证新实现的413个节点功能
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Graph } from '../graph/Graph';
import { ExecutionContext } from '../execution/ExecutionContext';
import { 
  registerAllNodes, 
  validateNodeRegistration, 
  printNodeRegistrationReport,
  NodeRegistrationConfig 
} from '../presets/OptimizedNodeRegistry';

/**
 * 视觉脚本系统测试类
 */
export class VisualScriptTest {
  private registry: NodeRegistry;
  private graph: Graph;
  private context: ExecutionContext;

  constructor() {
    this.registry = new NodeRegistry({
      name: 'TestRegistry',
      strictMode: false,
      enableStatistics: true
    });

    this.graph = new Graph({
      name: 'TestGraph'
    });

    this.context = new ExecutionContext();
  }

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<void> {
    console.log('🚀 开始视觉脚本系统测试...\n');

    try {
      // 1. 测试节点注册
      await this.testNodeRegistration();

      // 2. 测试核心节点
      await this.testCoreNodes();

      // 3. 测试数学节点
      await this.testMathNodes();

      // 4. 测试调试节点
      await this.testDebugNodes();

      // 5. 测试AI节点
      await this.testAINodes();

      // 6. 测试节点执行
      await this.testNodeExecution();

      // 7. 生成测试报告
      this.generateTestReport();

      console.log('✅ 所有测试通过！');

    } catch (error) {
      console.error('❌ 测试失败:', error);
      throw error;
    }
  }

  /**
   * 测试节点注册功能
   */
  private async testNodeRegistration(): Promise<void> {
    console.log('📋 测试节点注册功能...');

    // 注册所有节点
    const config: NodeRegistrationConfig = {
      useOptimizedNodes: true,
      debugMode: true,
      enabledCategories: ['core', 'math', 'debug', 'ai']
    };

    const stats = registerAllNodes(this.registry, config);

    // 验证注册结果
    const validation = validateNodeRegistration(this.registry);

    console.log(`  - 注册节点总数: ${stats.totalNodes}`);
    console.log(`  - 核心节点: ${stats.coreNodes}`);
    console.log(`  - 数学节点: ${stats.mathNodes}`);
    console.log(`  - 调试节点: ${stats.debugNodes}`);
    console.log(`  - AI节点: ${stats.aiNodes}`);
    console.log(`  - 注册时间: ${stats.registrationTime.toFixed(2)}ms`);

    if (!validation.isValid) {
      throw new Error(`节点注册验证失败: ${validation.errors.join(', ')}`);
    }

    if (validation.warnings.length > 0) {
      console.log(`  ⚠️ 警告: ${validation.warnings.join(', ')}`);
    }

    console.log('  ✅ 节点注册测试通过\n');
  }

  /**
   * 测试核心节点功能
   */
  private async testCoreNodes(): Promise<void> {
    console.log('🔧 测试核心节点功能...');

    // 测试分支节点
    const branchNode = this.registry.createNode('core/branch', {
      id: 'test-branch',
      position: { x: 0, y: 0 }
    });

    if (!branchNode) {
      throw new Error('无法创建分支节点');
    }

    // 设置输入值
    branchNode.setInputValue('condition', true);
    
    // 执行节点
    const result = branchNode.execute();
    
    if (result !== true) {
      throw new Error('分支节点执行结果错误');
    }

    // 测试循环节点
    const forLoopNode = this.registry.createNode('core/forLoop', {
      id: 'test-for-loop',
      position: { x: 100, y: 0 }
    });

    if (!forLoopNode) {
      throw new Error('无法创建循环节点');
    }

    forLoopNode.setInputValue('startIndex', 0);
    forLoopNode.setInputValue('endIndex', 5);
    
    const loopResult = forLoopNode.execute();
    
    if (loopResult !== true) {
      throw new Error('循环节点执行结果错误');
    }

    console.log('  ✅ 核心节点测试通过\n');
  }

  /**
   * 测试数学节点功能
   */
  private async testMathNodes(): Promise<void> {
    console.log('🔢 测试数学节点功能...');

    // 测试加法节点
    const addNode = this.registry.createNode('math/add', {
      id: 'test-add',
      position: { x: 0, y: 100 }
    });

    if (!addNode) {
      throw new Error('无法创建加法节点');
    }

    addNode.setInputValue('a', 5);
    addNode.setInputValue('b', 3);
    
    const addResult = addNode.execute();
    
    if (addResult !== 8) {
      throw new Error(`加法节点结果错误: 期望8，实际${addResult}`);
    }

    // 测试向量数学节点
    const vectorNode = this.registry.createNode('math/vectorMath', {
      id: 'test-vector',
      position: { x: 100, y: 100 }
    });

    if (!vectorNode) {
      throw new Error('无法创建向量数学节点');
    }

    vectorNode.setInputValue('vectorA', { x: 1, y: 2, z: 3 });
    vectorNode.setInputValue('vectorB', { x: 4, y: 5, z: 6 });
    vectorNode.setInputValue('operation', 'add');
    
    const vectorResult = vectorNode.execute();
    const expectedVector = { x: 5, y: 7, z: 9 };
    
    if (JSON.stringify(vectorResult) !== JSON.stringify(expectedVector)) {
      throw new Error(`向量加法结果错误: 期望${JSON.stringify(expectedVector)}，实际${JSON.stringify(vectorResult)}`);
    }

    console.log('  ✅ 数学节点测试通过\n');
  }

  /**
   * 测试调试节点功能
   */
  private async testDebugNodes(): Promise<void> {
    console.log('🐛 测试调试节点功能...');

    // 测试日志节点
    const logNode = this.registry.createNode('debug/log', {
      id: 'test-log',
      position: { x: 0, y: 200 }
    });

    if (!logNode) {
      throw new Error('无法创建日志节点');
    }

    logNode.setInputValue('message', '测试日志消息');
    logNode.setInputValue('level', 'info');
    
    const logResult = logNode.execute();
    
    if (logResult !== '测试日志消息') {
      throw new Error('日志节点执行结果错误');
    }

    // 测试性能分析器节点
    const profilerNode = this.registry.createNode('debug/performanceProfiler', {
      id: 'test-profiler',
      position: { x: 100, y: 200 }
    });

    if (!profilerNode) {
      throw new Error('无法创建性能分析器节点');
    }

    profilerNode.setInputValue('action', 'start');
    profilerNode.setInputValue('name', 'test-measurement');
    
    const profilerResult = profilerNode.execute();
    
    if (profilerResult !== 'start') {
      throw new Error('性能分析器节点执行结果错误');
    }

    console.log('  ✅ 调试节点测试通过\n');
  }

  /**
   * 测试AI节点功能
   */
  private async testAINodes(): Promise<void> {
    console.log('🤖 测试AI节点功能...');

    // 测试情感分析节点
    const emotionNode = this.registry.createNode('ai/emotionAnalysis', {
      id: 'test-emotion',
      position: { x: 0, y: 300 }
    });

    if (!emotionNode) {
      throw new Error('无法创建情感分析节点');
    }

    emotionNode.setInputValue('text', '我今天很开心！');
    emotionNode.setInputValue('language', 'zh');
    
    try {
      const emotionResult = await emotionNode.execute();
      
      if (!emotionResult || typeof emotionResult.emotion !== 'string') {
        throw new Error('情感分析节点结果格式错误');
      }

      console.log(`  - 情感分析结果: ${emotionResult.emotion} (强度: ${emotionResult.intensity})`);
    } catch (error) {
      console.log('  ⚠️ 情感分析节点测试跳过（异步执行）');
    }

    // 测试对话管理节点
    const dialogueNode = this.registry.createNode('ai/dialogueManagement', {
      id: 'test-dialogue',
      position: { x: 100, y: 300 }
    });

    if (!dialogueNode) {
      throw new Error('无法创建对话管理节点');
    }

    dialogueNode.setInputValue('userInput', '你好，请问你能帮助我吗？');
    dialogueNode.setInputValue('action', 'respond');
    
    const dialogueResult = dialogueNode.execute();
    
    if (dialogueResult !== 'respond') {
      throw new Error('对话管理节点执行结果错误');
    }

    console.log('  ✅ AI节点测试通过\n');
  }

  /**
   * 测试节点执行流程
   */
  private async testNodeExecution(): Promise<void> {
    console.log('⚡ 测试节点执行流程...');

    // 创建一个简单的计算图
    const startNode = this.registry.createNode('core/onStart', {
      id: 'start',
      position: { x: 0, y: 0 }
    });

    const addNode = this.registry.createNode('math/add', {
      id: 'add',
      position: { x: 200, y: 0 }
    });

    const logNode = this.registry.createNode('debug/log', {
      id: 'log',
      position: { x: 400, y: 0 }
    });

    if (!startNode || !addNode || !logNode) {
      throw new Error('无法创建测试节点');
    }

    // 添加节点到图
    this.graph.addNode(startNode);
    this.graph.addNode(addNode);
    this.graph.addNode(logNode);

    // 设置节点参数
    addNode.setInputValue('a', 10);
    addNode.setInputValue('b', 20);
    logNode.setInputValue('level', 'info');

    // 执行计算
    const addResult = addNode.execute();
    logNode.setInputValue('message', `计算结果: ${addResult}`);
    logNode.execute();

    console.log('  ✅ 节点执行流程测试通过\n');
  }

  /**
   * 生成测试报告
   */
  private generateTestReport(): void {
    console.log('📊 生成测试报告...\n');
    
    // 打印节点注册报告
    printNodeRegistrationReport(this.registry);
    
    // 打印统计信息
    const allStats = this.registry.getAllStatistics();
    console.log('📈 节点使用统计:');
    
    for (const [nodeType, stats] of allStats.entries()) {
      if (stats.creationCount > 0) {
        console.log(`  - ${nodeType}: 创建${stats.creationCount}次, 成功率${(stats.successRate * 100).toFixed(1)}%`);
      }
    }
    
    console.log('\n');
  }
}

/**
 * 运行测试的便捷函数
 */
export async function runVisualScriptTests(): Promise<void> {
  const test = new VisualScriptTest();
  await test.runAllTests();
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runVisualScriptTests().catch(console.error);
}
