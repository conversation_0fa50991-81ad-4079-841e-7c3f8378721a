/**
 * 调试节点实现
 * 包含日志输出、断点、性能监控、内存监控等调试工具
 */
import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 日志节点
 */
export class LogNode extends FlowNode {
  public static readonly TYPE = 'debug/log';

  constructor(options: any) {
    super({
      ...options,
      type: LogNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput('exec', 'execution');
    this.addInput('message', 'any');
    this.addInput('level', 'string'); // 'log', 'info', 'warn', 'error'
    this.addOutput('exec', 'execution');
  }

  protected executeImpl(): any {
    const message = this.getInputValue('message', '');
    const level = this.getInputValue('level', 'log');
    
    // 格式化消息
    const formattedMessage = `[VisualScript] ${this.id}: ${message}`;
    
    switch (level) {
      case 'info':
        console.info(formattedMessage);
        break;
      case 'warn':
        console.warn(formattedMessage);
        break;
      case 'error':
        console.error(formattedMessage);
        break;
      default:
        console.log(formattedMessage);
    }
    
    this.triggerOutput('exec');
    return message;
  }
}

/**
 * 断点节点
 */
export class BreakpointNode extends FlowNode {
  public static readonly TYPE = 'debug/breakpoint';

  constructor(options: any) {
    super({
      ...options,
      type: BreakpointNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput('exec', 'execution');
    this.addInput('condition', 'boolean');
    this.addInput('message', 'string');
    this.addOutput('exec', 'execution');
  }

  protected executeImpl(): any {
    const condition = this.getInputValue('condition', true);
    const message = this.getInputValue('message', '断点触发');
    
    if (condition) {
      console.log(`🔴 断点: ${message}`);
      
      // 在开发环境中触发调试器
      if (process.env.NODE_ENV === 'development') {
        debugger;
      }
      
      // 发出断点事件
      this.emit('breakpointHit', { message, node: this });
    }
    
    this.triggerOutput('exec');
    return condition;
  }
}

/**
 * 性能分析器节点
 */
export class PerformanceProfilerNode extends FlowNode {
  public static readonly TYPE = 'debug/performanceProfiler';
  private startTime: number = 0;
  private measurements: Array<{ name: string; duration: number; timestamp: number }> = [];

  constructor(options: any) {
    super({
      ...options,
      type: PerformanceProfilerNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput('exec', 'execution');
    this.addInput('action', 'string'); // 'start', 'end', 'mark', 'report'
    this.addInput('name', 'string');
    this.addOutput('exec', 'execution');
    this.addOutput('duration', 'number');
    this.addOutput('report', 'object');
  }

  protected executeImpl(): any {
    const action = this.getInputValue('action', 'start');
    const name = this.getInputValue('name', 'measurement');
    
    switch (action) {
      case 'start':
        this.startTime = performance.now();
        console.log(`⏱️ 性能测量开始: ${name}`);
        break;
        
      case 'end':
        if (this.startTime > 0) {
          const duration = performance.now() - this.startTime;
          this.measurements.push({
            name,
            duration,
            timestamp: Date.now()
          });
          
          console.log(`⏱️ 性能测量结束: ${name} - ${duration.toFixed(2)}ms`);
          this.setOutputValue('duration', duration);
          this.startTime = 0;
        }
        break;
        
      case 'mark':
        const markTime = performance.now();
        console.log(`📍 性能标记: ${name} - ${markTime.toFixed(2)}ms`);
        break;
        
      case 'report':
        const report = {
          measurements: this.measurements,
          totalMeasurements: this.measurements.length,
          averageDuration: this.measurements.length > 0 
            ? this.measurements.reduce((sum, m) => sum + m.duration, 0) / this.measurements.length 
            : 0
        };
        
        console.table(this.measurements);
        this.setOutputValue('report', report);
        break;
    }
    
    this.triggerOutput('exec');
    return action;
  }
}

/**
 * 内存监控节点
 */
export class MemoryMonitorNode extends Node {
  public static readonly TYPE = 'debug/memoryMonitor';

  constructor(options: any) {
    super({
      ...options,
      type: MemoryMonitorNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput('action', 'string'); // 'snapshot', 'compare', 'gc'
    this.addOutput('memoryInfo', 'object');
    this.addOutput('heapUsed', 'number');
    this.addOutput('heapTotal', 'number');
  }

  protected executeImpl(): any {
    const action = this.getInputValue('action', 'snapshot');
    
    // 获取内存信息
    const memoryInfo = this.getMemoryInfo();
    
    switch (action) {
      case 'snapshot':
        console.log('📊 内存快照:', memoryInfo);
        break;
        
      case 'gc':
        // 尝试触发垃圾回收（仅在Node.js环境中有效）
        if (typeof global !== 'undefined' && global.gc) {
          global.gc();
          console.log('🗑️ 垃圾回收已触发');
        } else {
          console.warn('⚠️ 垃圾回收不可用');
        }
        break;
    }
    
    this.setOutputValue('memoryInfo', memoryInfo);
    this.setOutputValue('heapUsed', memoryInfo.heapUsed);
    this.setOutputValue('heapTotal', memoryInfo.heapTotal);
    
    return memoryInfo;
  }

  private getMemoryInfo(): any {
    // 浏览器环境
    if (typeof window !== 'undefined' && (window as any).performance?.memory) {
      const memory = (window as any).performance.memory;
      return {
        heapUsed: memory.usedJSHeapSize,
        heapTotal: memory.totalJSHeapSize,
        heapLimit: memory.jsHeapSizeLimit,
        type: 'browser'
      };
    }
    
    // Node.js环境
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const memory = process.memoryUsage();
      return {
        heapUsed: memory.heapUsed,
        heapTotal: memory.heapTotal,
        external: memory.external,
        rss: memory.rss,
        type: 'node'
      };
    }
    
    // 默认返回
    return {
      heapUsed: 0,
      heapTotal: 0,
      type: 'unknown'
    };
  }
}

/**
 * 变量监视器节点
 */
export class VariableWatcherNode extends Node {
  public static readonly TYPE = 'debug/variableWatcher';
  private watchedVariables: Map<string, any> = new Map();

  constructor(options: any) {
    super({
      ...options,
      type: VariableWatcherNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput('variableName', 'string');
    this.addInput('action', 'string'); // 'watch', 'unwatch', 'list'
    this.addOutput('value', 'any');
    this.addOutput('changed', 'boolean');
    this.addOutput('watchList', 'array');
  }

  protected executeImpl(): any {
    const variableName = this.getInputValue('variableName', '');
    const action = this.getInputValue('action', 'watch');
    
    switch (action) {
      case 'watch':
        if (variableName) {
          const currentValue = this.context.getVariable(variableName);
          const previousValue = this.watchedVariables.get(variableName);
          const changed = previousValue !== currentValue;
          
          if (changed) {
            console.log(`👁️ 变量变化: ${variableName} = ${currentValue} (之前: ${previousValue})`);
          }
          
          this.watchedVariables.set(variableName, currentValue);
          this.setOutputValue('value', currentValue);
          this.setOutputValue('changed', changed);
        }
        break;
        
      case 'unwatch':
        if (variableName && this.watchedVariables.has(variableName)) {
          this.watchedVariables.delete(variableName);
          console.log(`👁️ 停止监视变量: ${variableName}`);
        }
        break;
        
      case 'list':
        const watchList = Array.from(this.watchedVariables.entries()).map(([name, value]) => ({
          name,
          value,
          type: typeof value
        }));
        
        console.table(watchList);
        this.setOutputValue('watchList', watchList);
        break;
    }
    
    return action;
  }
}

/**
 * 调用堆栈跟踪节点
 */
export class StackTraceNode extends Node {
  public static readonly TYPE = 'debug/stackTrace';

  constructor(options: any) {
    super({
      ...options,
      type: StackTraceNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput('capture', 'boolean');
    this.addOutput('stackTrace', 'string');
    this.addOutput('callStack', 'array');
  }

  protected executeImpl(): any {
    const capture = this.getInputValue('capture', true);
    
    if (capture) {
      const error = new Error();
      const stackTrace = error.stack || '';
      
      // 解析调用堆栈
      const callStack = stackTrace
        .split('\n')
        .slice(1) // 移除第一行（Error消息）
        .map(line => line.trim())
        .filter(line => line.length > 0);
      
      console.log('📚 调用堆栈:', callStack);
      
      this.setOutputValue('stackTrace', stackTrace);
      this.setOutputValue('callStack', callStack);
      
      return callStack;
    }
    
    return null;
  }
}

/**
 * 执行时间测量节点
 */
export class ExecutionTimerNode extends FlowNode {
  public static readonly TYPE = 'debug/executionTimer';
  private timers: Map<string, number> = new Map();

  constructor(options: any) {
    super({
      ...options,
      type: ExecutionTimerNode.TYPE
    });
  }

  protected initializeSockets(): void {
    this.addInput('exec', 'execution');
    this.addInput('timerName', 'string');
    this.addInput('action', 'string'); // 'start', 'stop', 'reset'
    this.addOutput('exec', 'execution');
    this.addOutput('elapsed', 'number');
  }

  protected executeImpl(): any {
    const timerName = this.getInputValue('timerName', 'default');
    const action = this.getInputValue('action', 'start');
    
    switch (action) {
      case 'start':
        this.timers.set(timerName, performance.now());
        console.log(`⏰ 计时器开始: ${timerName}`);
        break;
        
      case 'stop':
        const startTime = this.timers.get(timerName);
        if (startTime !== undefined) {
          const elapsed = performance.now() - startTime;
          console.log(`⏰ 计时器结束: ${timerName} - ${elapsed.toFixed(2)}ms`);
          this.setOutputValue('elapsed', elapsed);
          this.timers.delete(timerName);
        }
        break;
        
      case 'reset':
        this.timers.delete(timerName);
        console.log(`⏰ 计时器重置: ${timerName}`);
        break;
    }
    
    this.triggerOutput('exec');
    return action;
  }
}

/**
 * 注册调试节点到注册表
 */
export function registerDebugNodes(registry: NodeRegistry): void {
  registry.register(LogNode.TYPE, LogNode);
  registry.register(BreakpointNode.TYPE, BreakpointNode);
  registry.register(PerformanceProfilerNode.TYPE, PerformanceProfilerNode);
  registry.register(MemoryMonitorNode.TYPE, MemoryMonitorNode);
  registry.register(VariableWatcherNode.TYPE, VariableWatcherNode);
  registry.register(StackTraceNode.TYPE, StackTraceNode);
  registry.register(ExecutionTimerNode.TYPE, ExecutionTimerNode);
}
