/**
 * 核心节点实现
 * 包含流程控制、数据操作、异常处理等基础节点
 */
import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { EventNode } from '../nodes/EventNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';
import { Graph } from '../graph/Graph';

/**
 * 开始节点 - 脚本执行的入口点
 */
export class OnStartNode extends EventNode {
  public static readonly TYPE = 'core/onStart';

  constructor(options: any) {
    super({
      ...options,
      type: OnStartNode.TYPE
    });

    // 添加输出插槽
    this.addOutput('exec', 'execution');
  }

  protected initializeSockets(): void {
    // 开始节点没有输入插槽
  }

  protected executeImpl(): any {
    // 触发执行流
    this.triggerOutput('exec');
    return true;
  }

  public onStart(): void {
    // 脚本开始时自动执行
    this.execute();
  }
}

/**
 * 分支节点 - 条件判断
 */
export class BranchNode extends FlowNode {
  public static readonly TYPE = 'core/branch';

  constructor(options: any) {
    super({
      ...options,
      type: BranchNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('exec', 'execution');
    this.addInput('condition', 'boolean');

    // 输出插槽
    this.addOutput('true', 'execution');
    this.addOutput('false', 'execution');
  }

  protected executeImpl(): any {
    const condition = this.getInputValue('condition', false);
    
    if (condition) {
      this.triggerOutput('true');
    } else {
      this.triggerOutput('false');
    }

    return condition;
  }
}

/**
 * 序列节点 - 顺序执行多个操作
 */
export class SequenceNode extends FlowNode {
  public static readonly TYPE = 'core/sequence';

  constructor(options: any) {
    super({
      ...options,
      type: SequenceNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('exec', 'execution');

    // 输出插槽 - 支持多个顺序输出
    this.addOutput('exec1', 'execution');
    this.addOutput('exec2', 'execution');
    this.addOutput('exec3', 'execution');
    this.addOutput('exec4', 'execution');
  }

  protected executeImpl(): any {
    // 按顺序触发所有输出
    this.triggerOutput('exec1');
    this.triggerOutput('exec2');
    this.triggerOutput('exec3');
    this.triggerOutput('exec4');
    return true;
  }
}

/**
 * For循环节点
 */
export class ForLoopNode extends FlowNode {
  public static readonly TYPE = 'core/forLoop';
  private currentIndex: number = 0;

  constructor(options: any) {
    super({
      ...options,
      type: ForLoopNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('exec', 'execution');
    this.addInput('startIndex', 'number');
    this.addInput('endIndex', 'number');

    // 输出插槽
    this.addOutput('loopBody', 'execution');
    this.addOutput('completed', 'execution');
    this.addOutput('index', 'number');
  }

  protected executeImpl(): any {
    const startIndex = this.getInputValue('startIndex', 0);
    const endIndex = this.getInputValue('endIndex', 10);

    for (this.currentIndex = startIndex; this.currentIndex < endIndex; this.currentIndex++) {
      // 输出当前索引
      this.setOutputValue('index', this.currentIndex);
      
      // 执行循环体
      this.triggerOutput('loopBody');
    }

    // 循环完成
    this.triggerOutput('completed');
    return true;
  }
}

/**
 * While循环节点
 */
export class WhileLoopNode extends FlowNode {
  public static readonly TYPE = 'core/whileLoop';
  private maxIterations: number = 1000; // 防止无限循环

  constructor(options: any) {
    super({
      ...options,
      type: WhileLoopNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('exec', 'execution');
    this.addInput('condition', 'boolean');

    // 输出插槽
    this.addOutput('loopBody', 'execution');
    this.addOutput('completed', 'execution');
  }

  protected executeImpl(): any {
    let iterations = 0;

    while (this.getInputValue('condition', false) && iterations < this.maxIterations) {
      // 执行循环体
      this.triggerOutput('loopBody');
      iterations++;
    }

    // 循环完成
    this.triggerOutput('completed');
    return iterations;
  }
}

/**
 * 延迟节点
 */
export class DelayNode extends FlowNode {
  public static readonly TYPE = 'core/delay';

  constructor(options: any) {
    super({
      ...options,
      type: DelayNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('exec', 'execution');
    this.addInput('duration', 'number');

    // 输出插槽
    this.addOutput('completed', 'execution');
  }

  protected executeImpl(): any {
    const duration = this.getInputValue('duration', 1000); // 默认1秒

    setTimeout(() => {
      this.triggerOutput('completed');
    }, duration);

    return duration;
  }
}

/**
 * 设置变量节点
 */
export class SetVariableNode extends Node {
  public static readonly TYPE = 'core/setVariable';

  constructor(options: any) {
    super({
      ...options,
      type: SetVariableNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('exec', 'execution');
    this.addInput('variableName', 'string');
    this.addInput('value', 'any');

    // 输出插槽
    this.addOutput('exec', 'execution');
  }

  protected executeImpl(): any {
    const variableName = this.getInputValue('variableName', '');
    const value = this.getInputValue('value', null);

    if (variableName) {
      // 在执行上下文中设置变量
      this.context.setVariable(variableName, value);
    }

    this.triggerOutput('exec');
    return value;
  }
}

/**
 * 获取变量节点
 */
export class GetVariableNode extends Node {
  public static readonly TYPE = 'core/getVariable';

  constructor(options: any) {
    super({
      ...options,
      type: GetVariableNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('variableName', 'string');

    // 输出插槽
    this.addOutput('value', 'any');
  }

  protected executeImpl(): any {
    const variableName = this.getInputValue('variableName', '');
    const value = this.context.getVariable(variableName);
    
    this.setOutputValue('value', value);
    return value;
  }
}

/**
 * 数组操作节点
 */
export class ArrayOperationNode extends Node {
  public static readonly TYPE = 'core/arrayOperation';

  constructor(options: any) {
    super({
      ...options,
      type: ArrayOperationNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('exec', 'execution');
    this.addInput('array', 'array');
    this.addInput('operation', 'string'); // 'push', 'pop', 'get', 'set', 'length'
    this.addInput('index', 'number');
    this.addInput('value', 'any');

    // 输出插槽
    this.addOutput('exec', 'execution');
    this.addOutput('result', 'any');
    this.addOutput('array', 'array');
  }

  protected executeImpl(): any {
    const array = this.getInputValue('array', []);
    const operation = this.getInputValue('operation', 'get');
    const index = this.getInputValue('index', 0);
    const value = this.getInputValue('value', null);

    let result: any = null;

    switch (operation) {
      case 'push':
        result = array.push(value);
        break;
      case 'pop':
        result = array.pop();
        break;
      case 'get':
        result = array[index];
        break;
      case 'set':
        array[index] = value;
        result = value;
        break;
      case 'length':
        result = array.length;
        break;
      default:
        throw new Error(`不支持的数组操作: ${operation}`);
    }

    this.setOutputValue('result', result);
    this.setOutputValue('array', array);
    this.triggerOutput('exec');

    return result;
  }
}

/**
 * Try-Catch异常处理节点
 */
export class TryCatchNode extends FlowNode {
  public static readonly TYPE = 'core/tryCatch';

  constructor(options: any) {
    super({
      ...options,
      type: TryCatchNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('exec', 'execution');

    // 输出插槽
    this.addOutput('try', 'execution');
    this.addOutput('catch', 'execution');
    this.addOutput('finally', 'execution');
    this.addOutput('error', 'any');
  }

  protected executeImpl(): any {
    try {
      // 执行try块
      this.triggerOutput('try');
    } catch (error) {
      // 执行catch块
      this.setOutputValue('error', error);
      this.triggerOutput('catch');
    } finally {
      // 执行finally块
      this.triggerOutput('finally');
    }

    return true;
  }
}

/**
 * 类型转换节点
 */
export class TypeConvertNode extends Node {
  public static readonly TYPE = 'core/typeConvert';

  constructor(options: any) {
    super({
      ...options,
      type: TypeConvertNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput('value', 'any');
    this.addInput('targetType', 'string'); // 'string', 'number', 'boolean', 'array', 'object'

    // 输出插槽
    this.addOutput('result', 'any');
  }

  protected executeImpl(): any {
    const value = this.getInputValue('value', null);
    const targetType = this.getInputValue('targetType', 'string');

    let result: any = null;

    switch (targetType) {
      case 'string':
        result = String(value);
        break;
      case 'number':
        result = Number(value);
        break;
      case 'boolean':
        result = Boolean(value);
        break;
      case 'array':
        result = Array.isArray(value) ? value : [value];
        break;
      case 'object':
        result = typeof value === 'object' ? value : { value };
        break;
      default:
        result = value;
    }

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 注册核心节点到注册表
 */
export function registerCoreNodes(registry: NodeRegistry): void {
  registry.register(OnStartNode.TYPE, OnStartNode);
  registry.register(BranchNode.TYPE, BranchNode);
  registry.register(SequenceNode.TYPE, SequenceNode);
  registry.register(ForLoopNode.TYPE, ForLoopNode);
  registry.register(WhileLoopNode.TYPE, WhileLoopNode);
  registry.register(DelayNode.TYPE, DelayNode);
  registry.register(SetVariableNode.TYPE, SetVariableNode);
  registry.register(GetVariableNode.TYPE, GetVariableNode);
  registry.register(ArrayOperationNode.TYPE, ArrayOperationNode);
  registry.register(TryCatchNode.TYPE, TryCatchNode);
  registry.register(TypeConvertNode.TYPE, TypeConvertNode);
}
