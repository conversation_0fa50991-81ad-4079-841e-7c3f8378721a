/**
 * 操作转换(Operational Transform)算法实现
 * 用于处理并发编辑操作，确保操作的一致性和收敛性
 */

// 操作类型枚举
export enum OperationType {
  INSERT = 'insert',
  DELETE = 'delete',
  RETAIN = 'retain',
  // 场景编辑特定操作
  CREATE_ENTITY = 'create_entity',
  DELETE_ENTITY = 'delete_entity',
  UPDATE_PROPERTY = 'update_property',
  MOVE_ENTITY = 'move_entity',
  // 组件操作
  ADD_COMPONENT = 'add_component',
  REMOVE_COMPONENT = 'remove_component',
  UPDATE_COMPONENT = 'update_component'
}

// 基础操作接口
export interface BaseOperation {
  type: OperationType;
  id: string;
  timestamp: number;
  userId: string;
  version: number;
}

// 文本操作
export interface TextOperation extends BaseOperation {
  type: OperationType.INSERT | OperationType.DELETE | OperationType.RETAIN;
  position: number;
  content?: string;
  length?: number;
}

// 实体操作
export interface EntityOperation extends BaseOperation {
  type: OperationType.CREATE_ENTITY | OperationType.DELETE_ENTITY | OperationType.MOVE_ENTITY;
  entityId: string;
  parentId?: string;
  position?: { x: number; y: number; z: number };
  data?: any;
}

// 属性操作
export interface PropertyOperation extends BaseOperation {
  type: OperationType.UPDATE_PROPERTY;
  entityId: string;
  propertyPath: string;
  oldValue: any;
  newValue: any;
}

// 组件操作
export interface ComponentOperation extends BaseOperation {
  type: OperationType.ADD_COMPONENT | OperationType.REMOVE_COMPONENT | OperationType.UPDATE_COMPONENT;
  entityId: string;
  componentType: string;
  componentData?: any;
  oldData?: any;
}

// 联合操作类型
export type Operation = TextOperation | EntityOperation | PropertyOperation | ComponentOperation;

// 操作结果
export interface TransformResult {
  operation1: Operation;
  operation2: Operation;
}

/**
 * 操作转换引擎
 */
export class OperationalTransformEngine {
  private static instance: OperationalTransformEngine;
  private operationHistory: Operation[] = [];
  private currentVersion: number = 0;

  private constructor() {}

  public static getInstance(): OperationalTransformEngine {
    if (!OperationalTransformEngine.instance) {
      OperationalTransformEngine.instance = new OperationalTransformEngine();
    }
    return OperationalTransformEngine.instance;
  }

  /**
   * 转换两个并发操作
   */
  public transform(op1: Operation, op2: Operation): TransformResult {
    // 检查操作类型组合
    if (this.isTextOperation(op1) && this.isTextOperation(op2)) {
      return this.transformTextOperations(op1 as TextOperation, op2 as TextOperation);
    } else if (this.isEntityOperation(op1) && this.isEntityOperation(op2)) {
      return this.transformEntityOperations(op1 as EntityOperation, op2 as EntityOperation);
    } else if (this.isPropertyOperation(op1) && this.isPropertyOperation(op2)) {
      return this.transformPropertyOperations(op1 as PropertyOperation, op2 as PropertyOperation);
    } else if (this.isComponentOperation(op1) && this.isComponentOperation(op2)) {
      return this.transformComponentOperations(op1 as ComponentOperation, op2 as ComponentOperation);
    } else {
      // 不同类型的操作，通常不需要转换
      return { operation1: op1, operation2: op2 };
    }
  }

  /**
   * 应用操作到状态
   */
  public applyOperation(operation: Operation, state: any): any {
    switch (operation.type) {
      case OperationType.INSERT:
        return this.applyTextInsert(operation as TextOperation, state);
      case OperationType.DELETE:
        return this.applyTextDelete(operation as TextOperation, state);
      case OperationType.CREATE_ENTITY:
        return this.applyCreateEntity(operation as EntityOperation, state);
      case OperationType.DELETE_ENTITY:
        return this.applyDeleteEntity(operation as EntityOperation, state);
      case OperationType.UPDATE_PROPERTY:
        return this.applyUpdateProperty(operation as PropertyOperation, state);
      case OperationType.MOVE_ENTITY:
        return this.applyMoveEntity(operation as EntityOperation, state);
      case OperationType.ADD_COMPONENT:
        return this.applyAddComponent(operation as ComponentOperation, state);
      case OperationType.REMOVE_COMPONENT:
        return this.applyRemoveComponent(operation as ComponentOperation, state);
      case OperationType.UPDATE_COMPONENT:
        return this.applyUpdateComponent(operation as ComponentOperation, state);
      default:
        return state;
    }
  }

  /**
   * 合成操作序列
   */
  public compose(operations: Operation[]): Operation[] {
    if (operations.length <= 1) return operations;

    const result: Operation[] = [];
    let current = operations[0];

    for (let i = 1; i < operations.length; i++) {
      const next = operations[i];
      const composed = this.composeTwo(current, next);
      
      if (composed) {
        current = composed;
      } else {
        result.push(current);
        current = next;
      }
    }
    
    result.push(current);
    return result;
  }

  /**
   * 获取操作的逆操作
   */
  public invert(operation: Operation, state: any): Operation | null {
    switch (operation.type) {
      case OperationType.INSERT:
        const insertOp = operation as TextOperation;
        return {
          ...insertOp,
          type: OperationType.DELETE,
          length: insertOp.content?.length || 0,
          content: undefined
        };
      
      case OperationType.DELETE:
        const deleteOp = operation as TextOperation;
        // 需要从状态中获取被删除的内容
        const deletedContent = this.getDeletedContent(deleteOp, state);
        return {
          ...deleteOp,
          type: OperationType.INSERT,
          content: deletedContent,
          length: undefined
        };
      
      case OperationType.CREATE_ENTITY:
        const createOp = operation as EntityOperation;
        return {
          ...createOp,
          type: OperationType.DELETE_ENTITY
        };
      
      case OperationType.DELETE_ENTITY:
        const deleteEntityOp = operation as EntityOperation;
        return {
          ...deleteEntityOp,
          type: OperationType.CREATE_ENTITY
        };
      
      case OperationType.UPDATE_PROPERTY:
        const updateOp = operation as PropertyOperation;
        return {
          ...updateOp,
          oldValue: updateOp.newValue,
          newValue: updateOp.oldValue
        };
      
      default:
        return null;
    }
  }

  // 私有方法

  private isTextOperation(op: Operation): boolean {
    return [OperationType.INSERT, OperationType.DELETE, OperationType.RETAIN].includes(op.type);
  }

  private isEntityOperation(op: Operation): boolean {
    return [OperationType.CREATE_ENTITY, OperationType.DELETE_ENTITY, OperationType.MOVE_ENTITY].includes(op.type);
  }

  private isPropertyOperation(op: Operation): boolean {
    return op.type === OperationType.UPDATE_PROPERTY;
  }

  private isComponentOperation(op: Operation): boolean {
    return [OperationType.ADD_COMPONENT, OperationType.REMOVE_COMPONENT, OperationType.UPDATE_COMPONENT].includes(op.type);
  }

  /**
   * 转换文本操作
   */
  private transformTextOperations(op1: TextOperation, op2: TextOperation): TransformResult {
    if (op1.type === OperationType.INSERT && op2.type === OperationType.INSERT) {
      // 两个插入操作
      if (op1.position <= op2.position) {
        return {
          operation1: op1,
          operation2: { ...op2, position: op2.position + (op1.content?.length || 0) }
        };
      } else {
        return {
          operation1: { ...op1, position: op1.position + (op2.content?.length || 0) },
          operation2: op2
        };
      }
    } else if (op1.type === OperationType.DELETE && op2.type === OperationType.DELETE) {
      // 两个删除操作
      if (op1.position <= op2.position) {
        return {
          operation1: op1,
          operation2: { ...op2, position: Math.max(op1.position, op2.position - (op1.length || 0)) }
        };
      } else {
        return {
          operation1: { ...op1, position: Math.max(op2.position, op1.position - (op2.length || 0)) },
          operation2: op2
        };
      }
    } else if (op1.type === OperationType.INSERT && op2.type === OperationType.DELETE) {
      // 插入和删除
      if (op1.position <= op2.position) {
        return {
          operation1: op1,
          operation2: { ...op2, position: op2.position + (op1.content?.length || 0) }
        };
      } else {
        return {
          operation1: { ...op1, position: Math.max(op2.position, op1.position - (op2.length || 0)) },
          operation2: op2
        };
      }
    } else if (op1.type === OperationType.DELETE && op2.type === OperationType.INSERT) {
      // 删除和插入
      if (op1.position <= op2.position) {
        return {
          operation1: op1,
          operation2: { ...op2, position: Math.max(op1.position, op2.position - (op1.length || 0)) }
        };
      } else {
        return {
          operation1: { ...op1, position: op1.position + (op2.content?.length || 0) },
          operation2: op2
        };
      }
    }

    return { operation1: op1, operation2: op2 };
  }

  /**
   * 转换实体操作
   */
  private transformEntityOperations(op1: EntityOperation, op2: EntityOperation): TransformResult {
    // 如果操作的是同一个实体
    if (op1.entityId === op2.entityId) {
      // 创建和删除冲突
      if (op1.type === OperationType.CREATE_ENTITY && op2.type === OperationType.DELETE_ENTITY) {
        // 创建优先
        return { operation1: op1, operation2: { ...op2, type: OperationType.CREATE_ENTITY } };
      } else if (op1.type === OperationType.DELETE_ENTITY && op2.type === OperationType.CREATE_ENTITY) {
        // 创建优先
        return { operation1: { ...op1, type: OperationType.CREATE_ENTITY }, operation2: op2 };
      }
    }

    return { operation1: op1, operation2: op2 };
  }

  /**
   * 转换属性操作
   */
  private transformPropertyOperations(op1: PropertyOperation, op2: PropertyOperation): TransformResult {
    // 如果操作的是同一个属性
    if (op1.entityId === op2.entityId && op1.propertyPath === op2.propertyPath) {
      // 使用时间戳决定优先级
      if (op1.timestamp < op2.timestamp) {
        return {
          operation1: op1,
          operation2: { ...op2, oldValue: op1.newValue }
        };
      } else {
        return {
          operation1: { ...op1, oldValue: op2.newValue },
          operation2: op2
        };
      }
    }

    return { operation1: op1, operation2: op2 };
  }

  /**
   * 转换组件操作
   */
  private transformComponentOperations(op1: ComponentOperation, op2: ComponentOperation): TransformResult {
    // 如果操作的是同一个组件
    if (op1.entityId === op2.entityId && op1.componentType === op2.componentType) {
      // 添加和删除冲突
      if (op1.type === OperationType.ADD_COMPONENT && op2.type === OperationType.REMOVE_COMPONENT) {
        // 添加优先
        return { operation1: op1, operation2: { ...op2, type: OperationType.ADD_COMPONENT } };
      } else if (op1.type === OperationType.REMOVE_COMPONENT && op2.type === OperationType.ADD_COMPONENT) {
        // 添加优先
        return { operation1: { ...op1, type: OperationType.ADD_COMPONENT }, operation2: op2 };
      }
    }

    return { operation1: op1, operation2: op2 };
  }

  /**
   * 合成两个操作
   */
  private composeTwo(op1: Operation, op2: Operation): Operation | null {
    // 只有相同类型的操作才能合成
    if (op1.type !== op2.type) return null;

    if (this.isTextOperation(op1) && this.isTextOperation(op2)) {
      const textOp1 = op1 as TextOperation;
      const textOp2 = op2 as TextOperation;

      if (textOp1.type === OperationType.INSERT && textOp2.type === OperationType.INSERT) {
        // 连续插入
        if (textOp1.position + (textOp1.content?.length || 0) === textOp2.position) {
          return {
            ...textOp1,
            content: (textOp1.content || '') + (textOp2.content || '')
          };
        }
      } else if (textOp1.type === OperationType.DELETE && textOp2.type === OperationType.DELETE) {
        // 连续删除
        if (textOp1.position === textOp2.position) {
          return {
            ...textOp1,
            length: (textOp1.length || 0) + (textOp2.length || 0)
          };
        }
      }
    }

    return null;
  }

  // 应用操作的具体实现
  private applyTextInsert(operation: TextOperation, state: string): string {
    const pos = operation.position;
    const content = operation.content || '';
    return state.slice(0, pos) + content + state.slice(pos);
  }

  private applyTextDelete(operation: TextOperation, state: string): string {
    const pos = operation.position;
    const length = operation.length || 0;
    return state.slice(0, pos) + state.slice(pos + length);
  }

  private applyCreateEntity(operation: EntityOperation, state: any): any {
    // 实现创建实体的逻辑
    return {
      ...state,
      entities: {
        ...state.entities,
        [operation.entityId]: operation.data
      }
    };
  }

  private applyDeleteEntity(operation: EntityOperation, state: any): any {
    // 实现删除实体的逻辑
    const newEntities = { ...state.entities };
    delete newEntities[operation.entityId];
    return {
      ...state,
      entities: newEntities
    };
  }

  private applyUpdateProperty(operation: PropertyOperation, state: any): any {
    // 实现更新属性的逻辑
    const entity = state.entities[operation.entityId];
    if (!entity) return state;

    const updatedEntity = this.setNestedProperty(entity, operation.propertyPath, operation.newValue);
    return {
      ...state,
      entities: {
        ...state.entities,
        [operation.entityId]: updatedEntity
      }
    };
  }

  private applyMoveEntity(operation: EntityOperation, state: any): any {
    // 实现移动实体的逻辑
    const entity = state.entities[operation.entityId];
    if (!entity) return state;

    return {
      ...state,
      entities: {
        ...state.entities,
        [operation.entityId]: {
          ...entity,
          position: operation.position,
          parentId: operation.parentId
        }
      }
    };
  }

  private applyAddComponent(operation: ComponentOperation, state: any): any {
    // 实现添加组件的逻辑
    const entity = state.entities[operation.entityId];
    if (!entity) return state;

    return {
      ...state,
      entities: {
        ...state.entities,
        [operation.entityId]: {
          ...entity,
          components: {
            ...entity.components,
            [operation.componentType]: operation.componentData
          }
        }
      }
    };
  }

  private applyRemoveComponent(operation: ComponentOperation, state: any): any {
    // 实现移除组件的逻辑
    const entity = state.entities[operation.entityId];
    if (!entity) return state;

    const newComponents = { ...entity.components };
    delete newComponents[operation.componentType];

    return {
      ...state,
      entities: {
        ...state.entities,
        [operation.entityId]: {
          ...entity,
          components: newComponents
        }
      }
    };
  }

  private applyUpdateComponent(operation: ComponentOperation, state: any): any {
    // 实现更新组件的逻辑
    const entity = state.entities[operation.entityId];
    if (!entity) return state;

    return {
      ...state,
      entities: {
        ...state.entities,
        [operation.entityId]: {
          ...entity,
          components: {
            ...entity.components,
            [operation.componentType]: operation.componentData
          }
        }
      }
    };
  }

  private getDeletedContent(operation: TextOperation, state: string): string {
    const pos = operation.position;
    const length = operation.length || 0;
    return state.slice(pos, pos + length);
  }

  private setNestedProperty(obj: any, path: string, value: any): any {
    const keys = path.split('.');
    const result = { ...obj };
    let current = result;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      current[key] = { ...current[key] };
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
    return result;
  }
}

/**
 * 协作操作管理器
 * 管理操作的应用、同步和冲突解决
 */
export class CollaborativeOperationManager {
  private otEngine: OperationalTransformEngine;
  private pendingOperations: Operation[] = [];
  private acknowledgedOperations: Operation[] = [];
  private localVersion: number = 0;
  private serverVersion: number = 0;
  private state: any = {};

  constructor() {
    this.otEngine = OperationalTransformEngine.getInstance();
  }

  /**
   * 应用本地操作
   */
  public applyLocalOperation(operation: Operation): void {
    // 设置操作版本
    operation.version = this.localVersion++;

    // 应用到本地状态
    this.state = this.otEngine.applyOperation(operation, this.state);

    // 添加到待确认队列
    this.pendingOperations.push(operation);

    // 发送到服务器
    this.sendOperationToServer(operation);
  }

  /**
   * 接收远程操作
   */
  public receiveRemoteOperation(operation: Operation): void {
    // 转换待确认的操作
    let transformedOperation = operation;

    for (const pendingOp of this.pendingOperations) {
      const result = this.otEngine.transform(transformedOperation, pendingOp);
      transformedOperation = result.operation1;
    }

    // 应用转换后的操作
    this.state = this.otEngine.applyOperation(transformedOperation, this.state);

    // 更新服务器版本
    this.serverVersion = Math.max(this.serverVersion, operation.version);
  }

  /**
   * 确认操作
   */
  public acknowledgeOperation(operationId: string): void {
    const index = this.pendingOperations.findIndex(op => op.id === operationId);
    if (index !== -1) {
      const operation = this.pendingOperations.splice(index, 1)[0];
      this.acknowledgedOperations.push(operation);
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): any {
    return this.state;
  }

  /**
   * 设置状态
   */
  public setState(newState: any): void {
    this.state = newState;
  }

  /**
   * 获取待确认操作数量
   */
  public getPendingOperationCount(): number {
    return this.pendingOperations.length;
  }

  private sendOperationToServer(operation: Operation): void {
    // 这里应该通过WebSocket发送操作到服务器
    // 实际实现中会调用协作服务的发送方法
  }
}

export default OperationalTransformEngine;
