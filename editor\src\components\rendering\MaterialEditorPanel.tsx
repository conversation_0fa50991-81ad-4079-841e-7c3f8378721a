/**
 * 材质编辑器面板组件
 * 提供PBR材质编辑和管理功能
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Slider,
  ColorPicker,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Select,
  Input,
  Upload,
  Progress,
  List,
  Tag,
  Modal,
  Form,
  Divider,
  Tooltip,
  Badge,
  Rate,
  Collapse
} from 'antd';
import {
  BgColorsOutlined,
  UploadOutlined,
  EyeOutlined,
  CopyOutlined,
  DeleteOutlined,
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined,
  ExperimentOutlined,
  StarOutlined,
  FolderOutlined,
  PictureOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import AdvancedMaterialService, {
  MaterialDefinition,
  MaterialProperties,
  MaterialType,
  TextureType,
  MaterialPreset,
  ProceduralMaterialParams
} from '../../services/AdvancedMaterialService';
import './MaterialEditorPanel.less';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Panel } = Collapse;

interface MaterialEditorPanelProps {
  visible: boolean;
  onClose: () => void;
  materialId?: string;
}

const MaterialEditorPanel: React.FC<MaterialEditorPanelProps> = ({
  visible,
  onClose,
  materialId
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [currentMaterial, setCurrentMaterial] = useState<MaterialDefinition | null>(null);
  const [materials, setMaterials] = useState<MaterialDefinition[]>([]);
  const [presets, setPresets] = useState<MaterialPreset[]>([]);
  const [activeTab, setActiveTab] = useState('properties');
  const [previewMode, setPreviewMode] = useState<'sphere' | 'cube' | 'plane'>('sphere');
  const [isGenerating, setIsGenerating] = useState(false);
  const [presetModalVisible, setPresetModalVisible] = useState(false);
  const [proceduralModalVisible, setProceduralModalVisible] = useState(false);

  const materialService = AdvancedMaterialService.getInstance();

  useEffect(() => {
    if (visible) {
      loadData();
      setupEventListeners();
    }

    return () => {
      cleanupEventListeners();
    };
  }, [visible, materialId]);

  const setupEventListeners = () => {
    materialService.on('materialCreated', handleMaterialCreated);
    materialService.on('materialUpdated', handleMaterialUpdated);
    materialService.on('materialDeleted', handleMaterialDeleted);
    materialService.on('presetApplied', handlePresetApplied);
  };

  const cleanupEventListeners = () => {
    materialService.off('materialCreated', handleMaterialCreated);
    materialService.off('materialUpdated', handleMaterialUpdated);
    materialService.off('materialDeleted', handleMaterialDeleted);
    materialService.off('presetApplied', handlePresetApplied);
  };

  const loadData = () => {
    setMaterials(materialService.getAllMaterials());
    setPresets(materialService.getPresets());
    
    if (materialId) {
      const material = materialService.getMaterial(materialId);
      if (material) {
        setCurrentMaterial(material);
        form.setFieldsValue(material.properties);
      }
    } else if (materials.length > 0) {
      setCurrentMaterial(materials[0]);
      form.setFieldsValue(materials[0].properties);
    }
  };

  const handleMaterialCreated = (material: MaterialDefinition) => {
    setMaterials(prev => [...prev, material]);
  };

  const handleMaterialUpdated = (material: MaterialDefinition) => {
    setMaterials(prev => prev.map(m => m.id === material.id ? material : m));
    if (currentMaterial?.id === material.id) {
      setCurrentMaterial(material);
    }
  };

  const handleMaterialDeleted = (material: MaterialDefinition) => {
    setMaterials(prev => prev.filter(m => m.id !== material.id));
    if (currentMaterial?.id === material.id) {
      setCurrentMaterial(materials.length > 1 ? materials[0] : null);
    }
  };

  const handlePresetApplied = ({ material }: any) => {
    if (currentMaterial?.id === material.id) {
      setCurrentMaterial(material);
      form.setFieldsValue(material.properties);
    }
  };

  const handlePropertyChange = (property: string, value: any) => {
    if (!currentMaterial) return;

    const updates = { [property]: value };
    materialService.updateMaterial(currentMaterial.id, updates);
  };

  const handleCreateNewMaterial = () => {
    const newProperties: MaterialProperties = {
      name: 'New Material',
      type: MaterialType.PBR,
      albedo: [0.8, 0.8, 0.8],
      emission: [0, 0, 0],
      emissionIntensity: 0,
      metallic: 0,
      roughness: 0.5,
      specular: 0.5,
      specularTint: 0,
      opacity: 1,
      alphaMode: 'OPAQUE',
      alphaCutoff: 0.5,
      normalScale: 1,
      heightScale: 0.1,
      occlusionStrength: 1,
      subsurfaceRadius: [0, 0, 0],
      subsurfaceColor: [1, 1, 1],
      anisotropy: 0,
      anisotropyRotation: 0,
      clearcoat: 0,
      clearcoatRoughness: 0,
      sheen: 0,
      sheenTint: 0,
      doubleSided: false,
      customProperties: {}
    };

    const material = materialService.createMaterial(newProperties);
    setCurrentMaterial(material);
    form.setFieldsValue(material.properties);
  };

  const handleCloneMaterial = () => {
    if (!currentMaterial) return;

    const cloned = materialService.cloneMaterial(currentMaterial.id);
    setCurrentMaterial(cloned);
    form.setFieldsValue(cloned.properties);
  };

  const handleDeleteMaterial = () => {
    if (!currentMaterial) return;

    Modal.confirm({
      title: t('material.confirmDelete'),
      content: t('material.confirmDeleteContent'),
      onOk: () => {
        materialService.deleteMaterial(currentMaterial.id);
      }
    });
  };

  const handleApplyPreset = (presetId: string) => {
    if (!currentMaterial) return;

    materialService.applyPreset(currentMaterial.id, presetId);
    setPresetModalVisible(false);
  };

  const handleGenerateProcedural = async (params: ProceduralMaterialParams) => {
    setIsGenerating(true);
    try {
      const material = await materialService.generateProceduralMaterial(
        currentMaterial?.properties || {},
        params
      );
      setCurrentMaterial(material);
      form.setFieldsValue(material.properties);
      setProceduralModalVisible(false);
    } catch (error) {
      console.error('Failed to generate procedural material:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // 渲染材质属性编辑器
  const renderPropertiesEditor = () => (
    <Form form={form} layout="vertical" onValuesChange={(changed, values) => {
      Object.entries(changed).forEach(([key, value]) => {
        handlePropertyChange(key, value);
      });
    }}>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Form.Item name="name" label={t('material.name')}>
            <Input />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="type" label={t('material.type')}>
            <Select>
              {Object.values(MaterialType).map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Divider>{t('material.baseProperties')}</Divider>

      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Form.Item name="albedo" label={t('material.albedo')}>
            <ColorPicker 
              value={currentMaterial?.properties.albedo}
              onChange={(color) => handlePropertyChange('albedo', [color.r/255, color.g/255, color.b/255])}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Form.Item name="metallic" label={t('material.metallic')}>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={currentMaterial?.properties.metallic}
              onChange={(value) => handlePropertyChange('metallic', value)}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="roughness" label={t('material.roughness')}>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={currentMaterial?.properties.roughness}
              onChange={(value) => handlePropertyChange('roughness', value)}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Form.Item name="specular" label={t('material.specular')}>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={currentMaterial?.properties.specular}
              onChange={(value) => handlePropertyChange('specular', value)}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="opacity" label={t('material.opacity')}>
            <Slider
              min={0}
              max={1}
              step={0.01}
              value={currentMaterial?.properties.opacity}
              onChange={(value) => handlePropertyChange('opacity', value)}
            />
          </Form.Item>
        </Col>
      </Row>

      <Divider>{t('material.advancedProperties')}</Divider>

      <Collapse size="small">
        <Panel header={t('material.emission')} key="emission">
          <Row gutter={[16, 16]}>
            <Col span={18}>
              <Form.Item name="emission" label={t('material.emissionColor')}>
                <ColorPicker 
                  value={currentMaterial?.properties.emission}
                  onChange={(color) => handlePropertyChange('emission', [color.r/255, color.g/255, color.b/255])}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="emissionIntensity" label={t('material.intensity')}>
                <Slider
                  min={0}
                  max={10}
                  step={0.1}
                  value={currentMaterial?.properties.emissionIntensity}
                  onChange={(value) => handlePropertyChange('emissionIntensity', value)}
                />
              </Form.Item>
            </Col>
          </Row>
        </Panel>

        <Panel header={t('material.clearcoat')} key="clearcoat">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="clearcoat" label={t('material.clearcoat')}>
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  value={currentMaterial?.properties.clearcoat}
                  onChange={(value) => handlePropertyChange('clearcoat', value)}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="clearcoatRoughness" label={t('material.clearcoatRoughness')}>
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  value={currentMaterial?.properties.clearcoatRoughness}
                  onChange={(value) => handlePropertyChange('clearcoatRoughness', value)}
                />
              </Form.Item>
            </Col>
          </Row>
        </Panel>

        <Panel header={t('material.sheen')} key="sheen">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="sheen" label={t('material.sheen')}>
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  value={currentMaterial?.properties.sheen}
                  onChange={(value) => handlePropertyChange('sheen', value)}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="sheenTint" label={t('material.sheenTint')}>
                <Slider
                  min={0}
                  max={1}
                  step={0.01}
                  value={currentMaterial?.properties.sheenTint}
                  onChange={(value) => handlePropertyChange('sheenTint', value)}
                />
              </Form.Item>
            </Col>
          </Row>
        </Panel>
      </Collapse>
    </Form>
  );

  // 渲染纹理编辑器
  const renderTextureEditor = () => (
    <div className="texture-editor">
      <Row gutter={[16, 16]}>
        {Object.values(TextureType).map(textureType => (
          <Col span={8} key={textureType}>
            <Card
              size="small"
              title={textureType}
              extra={
                <Upload
                  accept="image/*"
                  showUploadList={false}
                  beforeUpload={() => false}
                >
                  <Button type="text" icon={<UploadOutlined />} size="small" />
                </Upload>
              }
            >
              <div className="texture-preview">
                {currentMaterial?.textures.get(textureType) ? (
                  <img 
                    src={currentMaterial.textures.get(textureType)?.url} 
                    alt={textureType}
                    style={{ width: '100%', height: '80px', objectFit: 'cover' }}
                  />
                ) : (
                  <div className="no-texture">
                    <PictureOutlined style={{ fontSize: '24px', color: '#d9d9d9' }} />
                    <Text type="secondary" style={{ fontSize: '12px' }}>No Texture</Text>
                  </div>
                )}
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  // 渲染材质预览
  const renderPreview = () => (
    <div className="material-preview">
      <div className="preview-controls">
        <Space>
          <Select value={previewMode} onChange={setPreviewMode} size="small">
            <Option value="sphere">Sphere</Option>
            <Option value="cube">Cube</Option>
            <Option value="plane">Plane</Option>
          </Select>
          <Button type="text" icon={<ReloadOutlined />} size="small">
            {t('material.refresh')}
          </Button>
        </Space>
      </div>
      <div className="preview-viewport">
        {/* 3D预览区域 - 这里应该集成Three.js或其他3D引擎 */}
        <div style={{ 
          width: '100%', 
          height: '300px', 
          background: 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)',
          backgroundSize: '20px 20px',
          backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px solid #d9d9d9',
          borderRadius: '4px'
        }}>
          <Text type="secondary">3D Preview ({previewMode})</Text>
        </div>
      </div>
      {currentMaterial && (
        <div className="material-info">
          <Row gutter={[8, 8]}>
            <Col span={8}>
              <Text type="secondary">Complexity:</Text>
              <Progress 
                percent={currentMaterial.metadata.complexity} 
                size="small" 
                strokeColor="#faad14"
                showInfo={false}
              />
            </Col>
            <Col span={8}>
              <Text type="secondary">Performance:</Text>
              <Progress 
                percent={currentMaterial.metadata.performance} 
                size="small" 
                strokeColor="#52c41a"
                showInfo={false}
              />
            </Col>
            <Col span={8}>
              <Text type="secondary">Quality:</Text>
              <Progress 
                percent={currentMaterial.metadata.quality} 
                size="small" 
                strokeColor="#1890ff"
                showInfo={false}
              />
            </Col>
          </Row>
        </div>
      )}
    </div>
  );

  // 渲染材质库
  const renderLibrary = () => (
    <div className="material-library">
      <div className="library-controls">
        <Space>
          <Button type="primary" onClick={handleCreateNewMaterial}>
            {t('material.createNew')}
          </Button>
          <Button onClick={() => setPresetModalVisible(true)}>
            <StarOutlined />
            {t('material.presets')}
          </Button>
          <Button onClick={() => setProceduralModalVisible(true)}>
            <ExperimentOutlined />
            {t('material.procedural')}
          </Button>
        </Space>
      </div>

      <List
        dataSource={materials}
        renderItem={(material) => (
          <List.Item
            className={currentMaterial?.id === material.id ? 'selected' : ''}
            onClick={() => {
              setCurrentMaterial(material);
              form.setFieldsValue(material.properties);
            }}
            actions={[
              <Tooltip title={t('material.clone')}>
                <Button type="text" icon={<CopyOutlined />} size="small" onClick={(e) => {
                  e.stopPropagation();
                  handleCloneMaterial();
                }} />
              </Tooltip>,
              <Tooltip title={t('material.delete')}>
                <Button type="text" icon={<DeleteOutlined />} size="small" danger onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteMaterial();
                }} />
              </Tooltip>
            ]}
          >
            <List.Item.Meta
              avatar={
                <div className="material-thumbnail">
                  <BgColorsOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                </div>
              }
              title={material.properties.name}
              description={
                <div>
                  <Tag color="blue">{material.properties.type}</Tag>
                  <Tag color="green">{material.metadata.category}</Tag>
                  <br />
                  <Space size="small" style={{ marginTop: 4 }}>
                    <Rate disabled count={5} value={Math.round(material.metadata.quality / 20)} style={{ fontSize: '12px' }} />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {material.metadata.tags.join(', ')}
                    </Text>
                  </Space>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <BgColorsOutlined />
          {t('material.materialEditor')}
          {currentMaterial && (
            <Badge count={currentMaterial.textures.size} showZero>
              <Tag color="blue">{currentMaterial.properties.name}</Tag>
            </Badge>
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1400}
      footer={[
        <Button key="close" onClick={onClose}>
          {t('common.close')}
        </Button>,
        <Button key="save" type="primary" icon={<SaveOutlined />}>
          {t('material.save')}
        </Button>
      ]}
      className="material-editor-panel"
    >
      <Row gutter={[16, 0]} style={{ height: '70vh' }}>
        <Col span={6}>
          {renderLibrary()}
        </Col>
        <Col span={12}>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab={t('material.properties')} key="properties">
              {renderPropertiesEditor()}
            </TabPane>
            <TabPane tab={t('material.textures')} key="textures">
              {renderTextureEditor()}
            </TabPane>
          </Tabs>
        </Col>
        <Col span={6}>
          {renderPreview()}
        </Col>
      </Row>

      {/* 预设选择对话框 */}
      <Modal
        title={t('material.selectPreset')}
        open={presetModalVisible}
        onCancel={() => setPresetModalVisible(false)}
        footer={null}
        width={800}
      >
        <Row gutter={[16, 16]}>
          {presets.map(preset => (
            <Col span={8} key={preset.id}>
              <Card
                hoverable
                cover={<img src={preset.thumbnail} alt={preset.name} style={{ height: '120px', objectFit: 'cover' }} />}
                onClick={() => handleApplyPreset(preset.id)}
              >
                <Card.Meta
                  title={preset.name}
                  description={
                    <div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>{preset.description}</Text>
                      <br />
                      <Space wrap style={{ marginTop: 4 }}>
                        {preset.tags.map(tag => (
                          <Tag key={tag} size="small">{tag}</Tag>
                        ))}
                      </Space>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Modal>

      {/* 程序化材质生成对话框 */}
      <Modal
        title={t('material.proceduralGeneration')}
        open={proceduralModalVisible}
        onCancel={() => setProceduralModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          layout="vertical"
          onFinish={(values) => {
            const params: ProceduralMaterialParams = {
              type: values.type,
              seed: values.seed || Math.random() * 1000,
              scale: values.scale || 1,
              octaves: values.octaves || 4,
              persistence: values.persistence || 0.5,
              lacunarity: values.lacunarity || 2,
              colors: [[1, 1, 1], [0, 0, 0]],
              colorStops: [0, 1],
              customParams: {}
            };
            handleGenerateProcedural(params);
          }}
        >
          <Form.Item name="type" label={t('material.proceduralType')} rules={[{ required: true }]}>
            <Select>
              <Option value="noise">Noise</Option>
              <Option value="pattern">Pattern</Option>
              <Option value="gradient">Gradient</Option>
              <Option value="cellular">Cellular</Option>
              <Option value="fractal">Fractal</Option>
            </Select>
          </Form.Item>

          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item name="scale" label={t('material.scale')}>
                <Slider min={0.1} max={10} step={0.1} defaultValue={1} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="octaves" label={t('material.octaves')}>
                <Slider min={1} max={8} step={1} defaultValue={4} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={isGenerating} block>
              {t('material.generate')}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default MaterialEditorPanel;
