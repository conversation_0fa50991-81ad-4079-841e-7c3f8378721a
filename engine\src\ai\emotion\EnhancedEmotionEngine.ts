/**
 * 增强的情感计算引擎
 * 提供多模态情感分析、情感建模、情感生成等高级功能
 */
import { System } from '../../core/System';
import { EventEmitter } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';

/**
 * 基础情感类型
 */
export enum BasicEmotion {
  JOY = 'joy',
  SADNESS = 'sadness',
  ANGER = 'anger',
  FEAR = 'fear',
  SURPRISE = 'surprise',
  DISGUST = 'disgust',
  NEUTRAL = 'neutral'
}

/**
 * 复合情感类型
 */
export enum ComplexEmotion {
  LOVE = 'love',
  HATE = 'hate',
  PRIDE = 'pride',
  SHAME = 'shame',
  GUILT = 'guilt',
  ENVY = 'envy',
  GRATITUDE = 'gratitude',
  HOPE = 'hope',
  DESPAIR = 'despair',
  EXCITEMENT = 'excitement',
  BOREDOM = 'boredom',
  CONFUSION = 'confusion',
  CONFIDENCE = 'confidence',
  ANXIETY = 'anxiety',
  RELIEF = 'relief'
}

/**
 * 情感维度
 */
export interface EmotionDimensions {
  /** 效价（正负性） */
  valence: number; // -1 到 1
  /** 唤醒度 */
  arousal: number; // 0 到 1
  /** 支配性 */
  dominance: number; // 0 到 1
}

/**
 * 情感状态
 */
export interface EmotionState {
  /** 基础情感分布 */
  basicEmotions: Record<BasicEmotion, number>;
  /** 复合情感分布 */
  complexEmotions: Record<ComplexEmotion, number>;
  /** 情感维度 */
  dimensions: EmotionDimensions;
  /** 主导情感 */
  dominantEmotion: BasicEmotion | ComplexEmotion;
  /** 情感强度 */
  intensity: number;
  /** 情感稳定性 */
  stability: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 多模态输入
 */
export interface MultimodalInput {
  /** 文本输入 */
  text?: string;
  /** 语音特征 */
  audio?: AudioFeatures;
  /** 面部表情 */
  facial?: FacialFeatures;
  /** 身体姿态 */
  posture?: PostureFeatures;
  /** 生理信号 */
  physiological?: PhysiologicalFeatures;
  /** 上下文信息 */
  context?: ContextFeatures;
}

/**
 * 语音特征
 */
export interface AudioFeatures {
  /** 基频 */
  pitch: number;
  /** 音量 */
  volume: number;
  /** 语速 */
  speechRate: number;
  /** 音色 */
  timbre: number[];
  /** 停顿模式 */
  pausePattern: number[];
}

/**
 * 面部表情特征
 */
export interface FacialFeatures {
  /** 面部动作单元 */
  actionUnits: Record<string, number>;
  /** 眼部特征 */
  eyeFeatures: {
    openness: number;
    gazeDirection: { x: number; y: number };
    blinkRate: number;
  };
  /** 嘴部特征 */
  mouthFeatures: {
    openness: number;
    curvature: number;
    asymmetry: number;
  };
  /** 眉毛特征 */
  browFeatures: {
    height: number;
    angle: number;
    furrow: number;
  };
}

/**
 * 身体姿态特征
 */
export interface PostureFeatures {
  /** 头部姿态 */
  headPose: { pitch: number; yaw: number; roll: number };
  /** 肩膀位置 */
  shoulderPosition: { left: number; right: number };
  /** 手势 */
  gestures: GestureFeature[];
  /** 整体姿态 */
  overallPosture: 'upright' | 'slouched' | 'leaning' | 'tense';
}

/**
 * 手势特征
 */
export interface GestureFeature {
  /** 手势类型 */
  type: string;
  /** 强度 */
  intensity: number;
  /** 持续时间 */
  duration: number;
  /** 频率 */
  frequency: number;
}

/**
 * 生理特征
 */
export interface PhysiologicalFeatures {
  /** 心率 */
  heartRate?: number;
  /** 皮肤电导 */
  skinConductance?: number;
  /** 体温 */
  bodyTemperature?: number;
  /** 呼吸频率 */
  respirationRate?: number;
}

/**
 * 上下文特征
 */
export interface ContextFeatures {
  /** 环境 */
  environment: string;
  /** 社交情境 */
  socialContext: string;
  /** 任务类型 */
  taskType: string;
  /** 时间信息 */
  timeInfo: {
    timeOfDay: number;
    dayOfWeek: number;
    season: string;
  };
}

/**
 * 情感历史记录
 */
export interface EmotionHistory {
  /** 用户ID */
  userId: string;
  /** 情感状态序列 */
  emotionSequence: EmotionState[];
  /** 情感模式 */
  patterns: EmotionPattern[];
  /** 触发因素 */
  triggers: EmotionTrigger[];
}

/**
 * 情感模式
 */
export interface EmotionPattern {
  /** 模式ID */
  id: string;
  /** 模式类型 */
  type: 'daily' | 'weekly' | 'situational' | 'social';
  /** 情感序列 */
  sequence: BasicEmotion[];
  /** 出现频率 */
  frequency: number;
  /** 置信度 */
  confidence: number;
}

/**
 * 情感触发因素
 */
export interface EmotionTrigger {
  /** 触发类型 */
  type: 'text' | 'audio' | 'visual' | 'social' | 'environmental';
  /** 触发内容 */
  content: string;
  /** 引发的情感 */
  emotion: BasicEmotion | ComplexEmotion;
  /** 强度 */
  intensity: number;
  /** 出现次数 */
  occurrences: number;
}

/**
 * 情感计算配置
 */
export interface EmotionConfig {
  /** 是否启用多模态分析 */
  enableMultimodal: boolean;
  /** 是否启用情感历史 */
  enableHistory: boolean;
  /** 是否启用情感预测 */
  enablePrediction: boolean;
  /** 是否启用实时分析 */
  enableRealtime: boolean;
  /** 情感更新频率 */
  updateFrequency: number;
  /** 历史记录长度 */
  historyLength: number;
  /** 模型配置 */
  models: {
    text: string;
    audio: string;
    facial: string;
    fusion: string;
  };
}

/**
 * 增强的情感计算引擎
 */
export class EnhancedEmotionEngine extends System {
  public static readonly TYPE = 'EnhancedEmotionEngine';

  private config: EmotionConfig;
  private eventEmitter: EventEmitter;
  private performanceMonitor: PerformanceMonitor;
  private emotionHistories: Map<string, EmotionHistory>;
  private currentStates: Map<string, EmotionState>;
  private emotionModels: Map<string, any>;
  private fusionWeights: Record<string, number>;

  constructor(config: EmotionConfig) {
    super();
    this.config = config;
    this.eventEmitter = new EventEmitter();
    this.performanceMonitor = new PerformanceMonitor();
    this.emotionHistories = new Map();
    this.currentStates = new Map();
    this.emotionModels = new Map();

    // 多模态融合权重
    this.fusionWeights = {
      text: 0.3,
      audio: 0.25,
      facial: 0.25,
      posture: 0.1,
      physiological: 0.1
    };

    this.initializeEngine();
  }

  /**
   * 初始化引擎
   */
  private initializeEngine(): void {
    this.performanceMonitor.start();
    this.loadEmotionModels();

    Debug.log('增强情感计算引擎初始化完成', {
      enableMultimodal: this.config.enableMultimodal,
      enableHistory: this.config.enableHistory,
      enablePrediction: this.config.enablePrediction
    });
  }

  /**
   * 加载情感模型
   */
  private async loadEmotionModels(): Promise<void> {
    // 加载各种情感分析模型
    const modelTypes = ['text', 'audio', 'facial', 'fusion'];

    for (const type of modelTypes) {
      // 这里应该加载实际的模型
      this.emotionModels.set(type, {
        type,
        loaded: true,
        version: '1.0.0'
      });
    }
  }

  /**
   * 分析情感
   */
  public async analyzeEmotion(
    userId: string,
    input: MultimodalInput
  ): Promise<EmotionState> {
    const startTime = performance.now();

    // 多模态情感分析
    const emotionResults: Record<string, EmotionState> = {};

    // 文本情感分析
    if (input.text) {
      emotionResults.text = await this.analyzeTextEmotion(input.text);
    }

    // 语音情感分析
    if (input.audio) {
      emotionResults.audio = await this.analyzeAudioEmotion(input.audio);
    }

    // 面部表情分析
    if (input.facial) {
      emotionResults.facial = await this.analyzeFacialEmotion(input.facial);
    }

    // 身体姿态分析
    if (input.posture) {
      emotionResults.posture = await this.analyzePostureEmotion(input.posture);
    }

    // 生理信号分析
    if (input.physiological) {
      emotionResults.physiological = await this.analyzePhysiologicalEmotion(input.physiological);
    }

    // 多模态融合
    const fusedEmotion = this.fuseEmotions(emotionResults);

    // 上下文调整
    if (input.context) {
      this.adjustEmotionWithContext(fusedEmotion, input.context);
    }

    // 更新情感历史
    if (this.config.enableHistory) {
      this.updateEmotionHistory(userId, fusedEmotion);
    }

    // 更新当前状态
    this.currentStates.set(userId, fusedEmotion);

    // 触发事件
    this.eventEmitter.emit('emotionAnalyzed', userId, fusedEmotion);

    const endTime = performance.now();
    Debug.log(`情感分析完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);

    return fusedEmotion;
  }

  /**
   * 文本情感分析
   */
  private async analyzeTextEmotion(text: string): Promise<EmotionState> {
    // 简化的文本情感分析
    const emotionKeywords = {
      [BasicEmotion.JOY]: ['开心', '快乐', '高兴', 'happy', 'joy', 'glad'],
      [BasicEmotion.SADNESS]: ['伤心', '难过', 'sad', 'sorrow', 'grief'],
      [BasicEmotion.ANGER]: ['愤怒', '生气', 'angry', 'mad', 'furious'],
      [BasicEmotion.FEAR]: ['害怕', '恐惧', 'fear', 'scared', 'afraid'],
      [BasicEmotion.SURPRISE]: ['惊讶', '意外', 'surprise', 'amazed', 'shocked'],
      [BasicEmotion.DISGUST]: ['厌恶', '恶心', 'disgust', 'revolting', 'gross']
    };

    const basicEmotions: Record<BasicEmotion, number> = {
      [BasicEmotion.JOY]: 0,
      [BasicEmotion.SADNESS]: 0,
      [BasicEmotion.ANGER]: 0,
      [BasicEmotion.FEAR]: 0,
      [BasicEmotion.SURPRISE]: 0,
      [BasicEmotion.DISGUST]: 0,
      [BasicEmotion.NEUTRAL]: 0.5
    };

    const lowerText = text.toLowerCase();
    let totalScore = 0;

    for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
      let score = 0;
      for (const keyword of keywords) {
        if (lowerText.includes(keyword)) {
          score += 1;
        }
      }
      basicEmotions[emotion as BasicEmotion] = score;
      totalScore += score;
    }

    // 归一化
    if (totalScore > 0) {
      for (const emotion of Object.keys(basicEmotions)) {
        basicEmotions[emotion as BasicEmotion] /= totalScore;
      }
      basicEmotions[BasicEmotion.NEUTRAL] = 0;
    }

    // 找到主导情感
    const dominantEmotion = Object.entries(basicEmotions).reduce((a, b) =>
      basicEmotions[a[0] as BasicEmotion] > basicEmotions[b[0] as BasicEmotion] ? a : b
    )[0] as BasicEmotion;

    return {
      basicEmotions,
      complexEmotions: {} as Record<ComplexEmotion, number>,
      dimensions: this.calculateEmotionDimensions(basicEmotions),
      dominantEmotion,
      intensity: Math.max(...Object.values(basicEmotions)),
      stability: 0.8,
      timestamp: Date.now()
    };
  }

  /**
   * 语音情感分析
   */
  private async analyzeAudioEmotion(audio: AudioFeatures): Promise<EmotionState> {
    const basicEmotions: Record<BasicEmotion, number> = {
      [BasicEmotion.JOY]: 0,
      [BasicEmotion.SADNESS]: 0,
      [BasicEmotion.ANGER]: 0,
      [BasicEmotion.FEAR]: 0,
      [BasicEmotion.SURPRISE]: 0,
      [BasicEmotion.DISGUST]: 0,
      [BasicEmotion.NEUTRAL]: 0.5
    };

    // 基于语音特征的简单情感识别
    if (audio.pitch > 200) {
      basicEmotions[BasicEmotion.SURPRISE] += 0.3;
      basicEmotions[BasicEmotion.JOY] += 0.2;
    } else if (audio.pitch < 100) {
      basicEmotions[BasicEmotion.SADNESS] += 0.3;
    }

    if (audio.volume > 0.8) {
      basicEmotions[BasicEmotion.ANGER] += 0.4;
    } else if (audio.volume < 0.3) {
      basicEmotions[BasicEmotion.SADNESS] += 0.2;
      basicEmotions[BasicEmotion.FEAR] += 0.2;
    }

    if (audio.speechRate > 1.5) {
      basicEmotions[BasicEmotion.SURPRISE] += 0.2;
      basicEmotions[BasicEmotion.ANGER] += 0.2;
    } else if (audio.speechRate < 0.8) {
      basicEmotions[BasicEmotion.SADNESS] += 0.3;
    }

    // 归一化
    const total = Object.values(basicEmotions).reduce((sum, val) => sum + val, 0);
    if (total > 0) {
      for (const emotion of Object.keys(basicEmotions)) {
        basicEmotions[emotion as BasicEmotion] /= total;
      }
    }

    const dominantEmotion = Object.entries(basicEmotions).reduce((a, b) =>
      basicEmotions[a[0] as BasicEmotion] > basicEmotions[b[0] as BasicEmotion] ? a : b
    )[0] as BasicEmotion;

    return {
      basicEmotions,
      complexEmotions: {} as Record<ComplexEmotion, number>,
      dimensions: this.calculateEmotionDimensions(basicEmotions),
      dominantEmotion,
      intensity: Math.max(...Object.values(basicEmotions)),
      stability: 0.7,
      timestamp: Date.now()
    };
  }

  /**
   * 面部表情情感分析
   */
  private async analyzeFacialEmotion(facial: FacialFeatures): Promise<EmotionState> {
    const basicEmotions: Record<BasicEmotion, number> = {
      [BasicEmotion.JOY]: 0,
      [BasicEmotion.SADNESS]: 0,
      [BasicEmotion.ANGER]: 0,
      [BasicEmotion.FEAR]: 0,
      [BasicEmotion.SURPRISE]: 0,
      [BasicEmotion.DISGUST]: 0,
      [BasicEmotion.NEUTRAL]: 0.5
    };

    // 基于面部特征的情感识别
    if (facial.mouthFeatures.curvature > 0.5) {
      basicEmotions[BasicEmotion.JOY] += 0.6;
    } else if (facial.mouthFeatures.curvature < -0.3) {
      basicEmotions[BasicEmotion.SADNESS] += 0.4;
    }

    if (facial.eyeFeatures.openness > 0.8) {
      basicEmotions[BasicEmotion.SURPRISE] += 0.4;
      basicEmotions[BasicEmotion.FEAR] += 0.2;
    } else if (facial.eyeFeatures.openness < 0.3) {
      basicEmotions[BasicEmotion.ANGER] += 0.3;
      basicEmotions[BasicEmotion.DISGUST] += 0.2;
    }

    if (facial.browFeatures.furrow > 0.5) {
      basicEmotions[BasicEmotion.ANGER] += 0.4;
      basicEmotions[BasicEmotion.SADNESS] += 0.2;
    }

    // 归一化
    const total = Object.values(basicEmotions).reduce((sum, val) => sum + val, 0);
    if (total > 0) {
      for (const emotion of Object.keys(basicEmotions)) {
        basicEmotions[emotion as BasicEmotion] /= total;
      }
    }

    const dominantEmotion = Object.entries(basicEmotions).reduce((a, b) =>
      basicEmotions[a[0] as BasicEmotion] > basicEmotions[b[0] as BasicEmotion] ? a : b
    )[0] as BasicEmotion;

    return {
      basicEmotions,
      complexEmotions: {} as Record<ComplexEmotion, number>,
      dimensions: this.calculateEmotionDimensions(basicEmotions),
      dominantEmotion,
      intensity: Math.max(...Object.values(basicEmotions)),
      stability: 0.9,
      timestamp: Date.now()
    };
  }

  /**
   * 身体姿态情感分析
   */
  private async analyzePostureEmotion(posture: PostureFeatures): Promise<EmotionState> {
    const basicEmotions: Record<BasicEmotion, number> = {
      [BasicEmotion.JOY]: 0,
      [BasicEmotion.SADNESS]: 0,
      [BasicEmotion.ANGER]: 0,
      [BasicEmotion.FEAR]: 0,
      [BasicEmotion.SURPRISE]: 0,
      [BasicEmotion.DISGUST]: 0,
      [BasicEmotion.NEUTRAL]: 0.5
    };

    // 基于姿态的情感识别
    switch (posture.overallPosture) {
      case 'upright':
        basicEmotions[BasicEmotion.JOY] += 0.3;
        break;
      case 'slouched':
        basicEmotions[BasicEmotion.SADNESS] += 0.4;
        break;
      case 'tense':
        basicEmotions[BasicEmotion.ANGER] += 0.3;
        basicEmotions[BasicEmotion.FEAR] += 0.2;
        break;
    }

    // 手势分析
    for (const gesture of posture.gestures) {
      if (gesture.type === 'pointing' && gesture.intensity > 0.7) {
        basicEmotions[BasicEmotion.ANGER] += 0.2;
      } else if (gesture.type === 'waving' && gesture.intensity > 0.5) {
        basicEmotions[BasicEmotion.JOY] += 0.2;
      }
    }

    const dominantEmotion = Object.entries(basicEmotions).reduce((a, b) =>
      basicEmotions[a[0] as BasicEmotion] > basicEmotions[b[0] as BasicEmotion] ? a : b
    )[0] as BasicEmotion;

    return {
      basicEmotions,
      complexEmotions: {} as Record<ComplexEmotion, number>,
      dimensions: this.calculateEmotionDimensions(basicEmotions),
      dominantEmotion,
      intensity: Math.max(...Object.values(basicEmotions)),
      stability: 0.6,
      timestamp: Date.now()
    };
  }

  /**
   * 生理信号情感分析
   */
  private async analyzePhysiologicalEmotion(physiological: PhysiologicalFeatures): Promise<EmotionState> {
    const basicEmotions: Record<BasicEmotion, number> = {
      [BasicEmotion.JOY]: 0,
      [BasicEmotion.SADNESS]: 0,
      [BasicEmotion.ANGER]: 0,
      [BasicEmotion.FEAR]: 0,
      [BasicEmotion.SURPRISE]: 0,
      [BasicEmotion.DISGUST]: 0,
      [BasicEmotion.NEUTRAL]: 0.5
    };

    // 基于生理信号的情感识别
    if (physiological.heartRate && physiological.heartRate > 100) {
      basicEmotions[BasicEmotion.FEAR] += 0.3;
      basicEmotions[BasicEmotion.ANGER] += 0.2;
      basicEmotions[BasicEmotion.SURPRISE] += 0.2;
    } else if (physiological.heartRate && physiological.heartRate < 60) {
      basicEmotions[BasicEmotion.SADNESS] += 0.3;
    }

    if (physiological.skinConductance && physiological.skinConductance > 0.8) {
      basicEmotions[BasicEmotion.FEAR] += 0.4;
      basicEmotions[BasicEmotion.ANGER] += 0.2;
    }

    const dominantEmotion = Object.entries(basicEmotions).reduce((a, b) =>
      basicEmotions[a[0] as BasicEmotion] > basicEmotions[b[0] as BasicEmotion] ? a : b
    )[0] as BasicEmotion;

    return {
      basicEmotions,
      complexEmotions: {} as Record<ComplexEmotion, number>,
      dimensions: this.calculateEmotionDimensions(basicEmotions),
      dominantEmotion,
      intensity: Math.max(...Object.values(basicEmotions)),
      stability: 0.8,
      timestamp: Date.now()
    };
  }

  /**
   * 多模态情感融合
   */
  private fuseEmotions(emotionResults: Record<string, EmotionState>): EmotionState {
    const fusedBasicEmotions: Record<BasicEmotion, number> = {
      [BasicEmotion.JOY]: 0,
      [BasicEmotion.SADNESS]: 0,
      [BasicEmotion.ANGER]: 0,
      [BasicEmotion.FEAR]: 0,
      [BasicEmotion.SURPRISE]: 0,
      [BasicEmotion.DISGUST]: 0,
      [BasicEmotion.NEUTRAL]: 0
    };

    let totalWeight = 0;

    // 加权融合各模态的情感结果
    for (const [modality, emotionState] of Object.entries(emotionResults)) {
      const weight = this.fusionWeights[modality] || 0.1;
      totalWeight += weight;

      for (const [emotion, value] of Object.entries(emotionState.basicEmotions)) {
        fusedBasicEmotions[emotion as BasicEmotion] += value * weight;
      }
    }

    // 归一化
    if (totalWeight > 0) {
      for (const emotion of Object.keys(fusedBasicEmotions)) {
        fusedBasicEmotions[emotion as BasicEmotion] /= totalWeight;
      }
    }

    // 计算复合情感
    const complexEmotions = this.calculateComplexEmotions(fusedBasicEmotions);

    // 找到主导情感
    const dominantEmotion = Object.entries(fusedBasicEmotions).reduce((a, b) =>
      fusedBasicEmotions[a[0] as BasicEmotion] > fusedBasicEmotions[b[0] as BasicEmotion] ? a : b
    )[0] as BasicEmotion;

    return {
      basicEmotions: fusedBasicEmotions,
      complexEmotions,
      dimensions: this.calculateEmotionDimensions(fusedBasicEmotions),
      dominantEmotion,
      intensity: Math.max(...Object.values(fusedBasicEmotions)),
      stability: this.calculateStability(Object.values(emotionResults)),
      timestamp: Date.now()
    };
  }

  /**
   * 计算情感维度
   */
  private calculateEmotionDimensions(basicEmotions: Record<BasicEmotion, number>): EmotionDimensions {
    // 效价计算（正负性）
    const positiveEmotions = basicEmotions[BasicEmotion.JOY] + basicEmotions[BasicEmotion.SURPRISE] * 0.5;
    const negativeEmotions = basicEmotions[BasicEmotion.SADNESS] + basicEmotions[BasicEmotion.ANGER] +
                            basicEmotions[BasicEmotion.FEAR] + basicEmotions[BasicEmotion.DISGUST];
    const valence = positiveEmotions - negativeEmotions;

    // 唤醒度计算
    const highArousalEmotions = basicEmotions[BasicEmotion.ANGER] + basicEmotions[BasicEmotion.FEAR] +
                               basicEmotions[BasicEmotion.SURPRISE] + basicEmotions[BasicEmotion.JOY] * 0.7;
    const arousal = Math.min(1, highArousalEmotions);

    // 支配性计算
    const dominanceEmotions = basicEmotions[BasicEmotion.ANGER] + basicEmotions[BasicEmotion.JOY] * 0.8 -
                             basicEmotions[BasicEmotion.FEAR] - basicEmotions[BasicEmotion.SADNESS] * 0.5;
    const dominance = Math.max(0, Math.min(1, (dominanceEmotions + 1) / 2));

    return { valence, arousal, dominance };
  }

  /**
   * 计算复合情感
   */
  private calculateComplexEmotions(basicEmotions: Record<BasicEmotion, number>): Record<ComplexEmotion, number> {
    const complexEmotions: Record<ComplexEmotion, number> = {} as Record<ComplexEmotion, number>;

    // 基于基础情感组合计算复合情感
    complexEmotions[ComplexEmotion.LOVE] = basicEmotions[BasicEmotion.JOY] * 0.8;
    complexEmotions[ComplexEmotion.HATE] = basicEmotions[BasicEmotion.ANGER] * 0.7 + basicEmotions[BasicEmotion.DISGUST] * 0.5;
    complexEmotions[ComplexEmotion.PRIDE] = basicEmotions[BasicEmotion.JOY] * 0.6;
    complexEmotions[ComplexEmotion.SHAME] = basicEmotions[BasicEmotion.SADNESS] * 0.6 + basicEmotions[BasicEmotion.FEAR] * 0.3;
    complexEmotions[ComplexEmotion.ANXIETY] = basicEmotions[BasicEmotion.FEAR] * 0.8;
    complexEmotions[ComplexEmotion.EXCITEMENT] = basicEmotions[BasicEmotion.JOY] * 0.7 + basicEmotions[BasicEmotion.SURPRISE] * 0.5;

    return complexEmotions;
  }

  /**
   * 计算稳定性
   */
  private calculateStability(emotionStates: EmotionState[]): number {
    if (emotionStates.length < 2) return 1.0;

    let totalVariance = 0;
    const emotions = Object.keys(BasicEmotion) as BasicEmotion[];

    for (const emotion of emotions) {
      const values = emotionStates.map(state => state.basicEmotions[emotion]);
      const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
      totalVariance += variance;
    }

    return Math.max(0, 1 - totalVariance);
  }

  /**
   * 上下文调整
   */
  private adjustEmotionWithContext(emotionState: EmotionState, context: ContextFeatures): void {
    // 根据环境调整情感
    if (context.environment === 'workplace') {
      // 工作环境中情感表达可能更克制
      emotionState.intensity *= 0.8;
    } else if (context.environment === 'home') {
      // 家庭环境中情感表达可能更自然
      emotionState.intensity *= 1.1;
    }

    // 根据社交情境调整
    if (context.socialContext === 'public') {
      emotionState.intensity *= 0.7;
    } else if (context.socialContext === 'private') {
      emotionState.intensity *= 1.2;
    }

    // 根据时间调整
    const hour = context.timeInfo.timeOfDay;
    if (hour < 6 || hour > 22) {
      // 深夜或清晨，情感可能更低沉
      emotionState.basicEmotions[BasicEmotion.SADNESS] *= 1.2;
      emotionState.basicEmotions[BasicEmotion.JOY] *= 0.8;
    }
  }

  /**
   * 更新情感历史
   */
  private updateEmotionHistory(userId: string, emotionState: EmotionState): void {
    let history = this.emotionHistories.get(userId);

    if (!history) {
      history = {
        userId,
        emotionSequence: [],
        patterns: [],
        triggers: []
      };
      this.emotionHistories.set(userId, history);
    }

    // 添加新的情感状态
    history.emotionSequence.push(emotionState);

    // 限制历史长度
    if (history.emotionSequence.length > this.config.historyLength) {
      history.emotionSequence.shift();
    }

    // 分析情感模式
    this.analyzeEmotionPatterns(history);
  }

  /**
   * 分析情感模式
   */
  private analyzeEmotionPatterns(history: EmotionHistory): void {
    if (history.emotionSequence.length < 5) return;

    // 简单的模式识别
    const recentEmotions = history.emotionSequence.slice(-5).map(state => state.dominantEmotion);

    // 检查是否有重复模式
    const patternLength = 3;
    for (let i = 0; i <= recentEmotions.length - patternLength * 2; i++) {
      const pattern1 = recentEmotions.slice(i, i + patternLength);
      const pattern2 = recentEmotions.slice(i + patternLength, i + patternLength * 2);

      if (JSON.stringify(pattern1) === JSON.stringify(pattern2)) {
        // 发现重复模式
        const existingPattern = history.patterns.find(p =>
          JSON.stringify(p.sequence) === JSON.stringify(pattern1)
        );

        if (existingPattern) {
          existingPattern.frequency++;
        } else {
          history.patterns.push({
            id: `pattern_${Date.now()}`,
            type: 'situational',
            sequence: pattern1 as BasicEmotion[],
            frequency: 1,
            confidence: 0.7
          });
        }
      }
    }
  }

  /**
   * 预测情感
   */
  public async predictEmotion(userId: string, timeHorizon: number = 3600000): Promise<EmotionState | null> {
    if (!this.config.enablePrediction) return null;

    const history = this.emotionHistories.get(userId);
    if (!history || history.emotionSequence.length < 3) return null;

    // 简单的情感预测基于历史趋势
    const recentStates = history.emotionSequence.slice(-3);
    const avgEmotions: Record<BasicEmotion, number> = {
      [BasicEmotion.JOY]: 0,
      [BasicEmotion.SADNESS]: 0,
      [BasicEmotion.ANGER]: 0,
      [BasicEmotion.FEAR]: 0,
      [BasicEmotion.SURPRISE]: 0,
      [BasicEmotion.DISGUST]: 0,
      [BasicEmotion.NEUTRAL]: 0
    };

    // 计算平均情感
    for (const state of recentStates) {
      for (const [emotion, value] of Object.entries(state.basicEmotions)) {
        avgEmotions[emotion as BasicEmotion] += value / recentStates.length;
      }
    }

    const dominantEmotion = Object.entries(avgEmotions).reduce((a, b) =>
      avgEmotions[a[0] as BasicEmotion] > avgEmotions[b[0] as BasicEmotion] ? a : b
    )[0] as BasicEmotion;

    return {
      basicEmotions: avgEmotions,
      complexEmotions: this.calculateComplexEmotions(avgEmotions),
      dimensions: this.calculateEmotionDimensions(avgEmotions),
      dominantEmotion,
      intensity: Math.max(...Object.values(avgEmotions)) * 0.8, // 预测强度稍低
      stability: 0.6,
      timestamp: Date.now() + timeHorizon
    };
  }

  /**
   * 获取当前情感状态
   */
  public getCurrentEmotion(userId: string): EmotionState | null {
    return this.currentStates.get(userId) || null;
  }

  /**
   * 获取情感历史
   */
  public getEmotionHistory(userId: string): EmotionHistory | null {
    return this.emotionHistories.get(userId) || null;
  }

  /**
   * 获取情感统计
   */
  public getEmotionStats(): any {
    return {
      activeUsers: this.currentStates.size,
      totalHistories: this.emotionHistories.size,
      averageHistoryLength: Array.from(this.emotionHistories.values())
        .reduce((sum, history) => sum + history.emotionSequence.length, 0) / this.emotionHistories.size || 0
    };
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    this.emotionHistories.clear();
    this.currentStates.clear();
    this.emotionModels.clear();
    this.performanceMonitor.stop();

    Debug.log('增强情感计算引擎已销毁');
  }
}