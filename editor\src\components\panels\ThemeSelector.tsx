/**
 * 主题选择器组件
 */
import React, { useState, useEffect } from 'react';
import {
  Select,
  Button,
  Dropdown,
  Menu,
  Modal,
  Form,
  Input,
  ColorPicker,
  Switch,
  Space,
  Card,
  Row,
  Col,
  message,
  Tooltip
} from 'antd';
import {
  BgColorsOutlined,
  SettingOutlined,
  PlusOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import PanelThemeManager, { ThemeConfig, ThemeColors } from '../../services/PanelThemeManager';
import './ThemeSelector.less';

interface ThemeSelectorProps {
  size?: 'small' | 'middle' | 'large';
  showLabel?: boolean;
  showCustomizer?: boolean;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  size = 'small',
  showLabel = false,
  showCustomizer = true
}) => {
  const { t } = useTranslation();
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>();
  const [themes, setThemes] = useState<ThemeConfig[]>([]);
  const [customizerVisible, setCustomizerVisible] = useState(false);
  const [form] = Form.useForm();

  const themeManager = PanelThemeManager.getInstance();

  useEffect(() => {
    loadThemes();
    
    // 监听主题变化事件
    const handleThemeChange = (event: CustomEvent) => {
      setCurrentTheme(event.detail.theme);
    };

    window.addEventListener('themeChanged', handleThemeChange as EventListener);
    return () => {
      window.removeEventListener('themeChanged', handleThemeChange as EventListener);
    };
  }, []);

  const loadThemes = () => {
    setThemes(themeManager.getAllThemes());
    setCurrentTheme(themeManager.getCurrentTheme());
  };

  // 切换主题
  const handleThemeChange = (themeId: string) => {
    if (themeManager.switchTheme(themeId)) {
      message.success(t('editor.theme.switched'));
    }
  };

  // 打开主题定制器
  const openCustomizer = () => {
    if (currentTheme) {
      form.setFieldsValue({
        name: `${currentTheme.name} (Custom)`,
        baseTheme: currentTheme.id,
        isDark: currentTheme.isDark,
        isCompact: currentTheme.isCompact,
        ...currentTheme.colors
      });
    }
    setCustomizerVisible(true);
  };

  // 创建自定义主题
  const handleCreateCustomTheme = async () => {
    try {
      const values = await form.validateFields();
      const customizations: Partial<ThemeColors> = {
        primary: values.primary,
        secondary: values.secondary,
        background: values.background,
        surface: values.surface,
        border: values.border,
        text: values.text,
        textSecondary: values.textSecondary,
        accent: values.accent,
        success: values.success,
        warning: values.warning,
        error: values.error,
        info: values.info
      };

      const customTheme = themeManager.createCustomTheme(
        `custom_${Date.now()}`,
        values.name,
        values.baseTheme,
        customizations
      );

      loadThemes();
      themeManager.switchTheme(customTheme.id);
      setCustomizerVisible(false);
      message.success(t('editor.theme.customThemeCreated'));
    } catch (error) {
      console.error('Failed to create custom theme:', error);
    }
  };

  // 删除自定义主题
  const handleDeleteTheme = (themeId: string) => {
    if (themeManager.deleteCustomTheme(themeId)) {
      loadThemes();
      message.success(t('editor.theme.themeDeleted'));
    } else {
      message.error(t('editor.theme.cannotDeleteBuiltinTheme'));
    }
  };

  // 导出主题
  const handleExportTheme = (themeId: string) => {
    const themeData = themeManager.exportTheme(themeId);
    if (themeData) {
      const blob = new Blob([themeData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `theme-${themeId}.json`;
      a.click();
      URL.revokeObjectURL(url);
      message.success(t('editor.theme.themeExported'));
    }
  };

  // 导入主题
  const handleImportTheme = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const themeData = e.target?.result as string;
      if (themeManager.importTheme(themeData)) {
        loadThemes();
        message.success(t('editor.theme.themeImported'));
      } else {
        message.error(t('editor.theme.importFailed'));
      }
    };
    reader.readAsText(file);
    return false;
  };

  // 主题操作菜单
  const getThemeMenu = (theme: ThemeConfig) => (
    <Menu
      items={[
        {
          key: 'export',
          label: t('editor.theme.export'),
          icon: <DownloadOutlined />,
          onClick: () => handleExportTheme(theme.id)
        },
        ...(theme.id.startsWith('custom_') ? [{
          key: 'delete',
          label: t('editor.theme.delete'),
          icon: <DeleteOutlined />,
          danger: true,
          onClick: () => handleDeleteTheme(theme.id)
        }] : [])
      ]}
    />
  );

  return (
    <div className="theme-selector">
      <Space size="small">
        {showLabel && <span>{t('editor.theme.theme')}:</span>}
        
        <Select
          value={currentTheme?.id}
          onChange={handleThemeChange}
          size={size}
          style={{ minWidth: 120 }}
          optionRender={(option) => (
            <div className="theme-option">
              <div className="theme-preview">
                <div 
                  className="color-dot" 
                  style={{ backgroundColor: themes.find(t => t.id === option.value)?.colors.primary }}
                />
                <span>{option.label}</span>
              </div>
              <Dropdown overlay={getThemeMenu(themes.find(t => t.id === option.value)!)} trigger={['click']}>
                <Button type="text" size="small" icon={<SettingOutlined />} />
              </Dropdown>
            </div>
          )}
        >
          {themes.map(theme => (
            <Select.Option key={theme.id} value={theme.id}>
              {theme.name}
            </Select.Option>
          ))}
        </Select>

        {showCustomizer && (
          <Tooltip title={t('editor.theme.customize')}>
            <Button
              type="text"
              icon={<BgColorsOutlined />}
              size={size}
              onClick={openCustomizer}
            />
          </Tooltip>
        )}
      </Space>

      {/* 主题定制器 */}
      <Modal
        title={t('editor.theme.customizeTheme')}
        open={customizerVisible}
        onOk={handleCreateCustomTheme}
        onCancel={() => setCustomizerVisible(false)}
        width={800}
        className="theme-customizer"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={t('editor.theme.themeName')}
                rules={[{ required: true, message: t('editor.theme.themeNameRequired') }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="baseTheme"
                label={t('editor.theme.baseTheme')}
                rules={[{ required: true }]}
              >
                <Select>
                  {themes.filter(t => !t.id.startsWith('custom_')).map(theme => (
                    <Select.Option key={theme.id} value={theme.id}>
                      {theme.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Card title={t('editor.theme.colorSettings')} size="small">
            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Form.Item name="primary" label={t('editor.theme.primaryColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="secondary" label={t('editor.theme.secondaryColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="background" label={t('editor.theme.backgroundColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="surface" label={t('editor.theme.surfaceColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="border" label={t('editor.theme.borderColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="text" label={t('editor.theme.textColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="textSecondary" label={t('editor.theme.secondaryTextColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="accent" label={t('editor.theme.accentColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          <Card title={t('editor.theme.statusColors')} size="small">
            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Form.Item name="success" label={t('editor.theme.successColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="warning" label={t('editor.theme.warningColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="error" label={t('editor.theme.errorColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name="info" label={t('editor.theme.infoColor')}>
                  <ColorPicker showText />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Form>
      </Modal>
    </div>
  );
};

export default ThemeSelector;
