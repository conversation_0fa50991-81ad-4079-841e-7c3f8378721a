/**
 * GPU粒子系统服务
 * 提供高性能粒子系统、粒子编辑器、预设效果库、物理模拟等功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 粒子系统类型枚举
export enum ParticleSystemType {
  FIRE = 'fire',
  SMOKE = 'smoke',
  EXPLOSION = 'explosion',
  SPARKS = 'sparks',
  RAIN = 'rain',
  SNOW = 'snow',
  DUST = 'dust',
  MAGIC = 'magic',
  ENERGY = 'energy',
  WATER = 'water',
  LEAVES = 'leaves',
  CUSTOM = 'custom'
}

// 发射器形状枚举
export enum EmitterShape {
  POINT = 'point',
  SPHERE = 'sphere',
  BOX = 'box',
  CONE = 'cone',
  CYLINDER = 'cylinder',
  PLANE = 'plane',
  MESH = 'mesh',
  EDGE = 'edge'
}

// 粒子混合模式枚举
export enum BlendMode {
  NORMAL = 'normal',
  ADDITIVE = 'additive',
  MULTIPLY = 'multiply',
  SCREEN = 'screen',
  ALPHA = 'alpha',
  PREMULTIPLIED = 'premultiplied'
}

// 粒子排序模式枚举
export enum SortMode {
  NONE = 'none',
  DISTANCE = 'distance',
  AGE = 'age',
  VELOCITY = 'velocity'
}

// 粒子属性接口
export interface ParticleProperties {
  // 基础属性
  name: string;
  type: ParticleSystemType;
  enabled: boolean;
  
  // 发射器属性
  emitterShape: EmitterShape;
  emitterSize: [number, number, number];
  emitterPosition: [number, number, number];
  emitterRotation: [number, number, number];
  
  // 粒子生成
  maxParticles: number;
  emissionRate: number;
  burstCount: number;
  burstInterval: number;
  
  // 粒子生命周期
  lifetime: {
    min: number;
    max: number;
  };
  
  // 粒子速度
  velocity: {
    initial: [number, number, number];
    randomness: [number, number, number];
  };
  
  // 粒子大小
  size: {
    initial: number;
    curve: number[];
    randomness: number;
  };
  
  // 粒子颜色
  color: {
    initial: [number, number, number, number];
    curve: [number, number, number, number][];
    randomness: number;
  };
  
  // 粒子旋转
  rotation: {
    initial: number;
    velocity: number;
    randomness: number;
  };
  
  // 物理属性
  gravity: [number, number, number];
  drag: number;
  mass: number;
  
  // 渲染属性
  material: {
    texture?: string;
    blendMode: BlendMode;
    sortMode: SortMode;
    billboard: boolean;
    softParticles: boolean;
  };
  
  // 碰撞属性
  collision: {
    enabled: boolean;
    bounce: number;
    friction: number;
    killOnCollision: boolean;
  };
  
  // 噪声属性
  noise: {
    enabled: boolean;
    strength: number;
    frequency: number;
    octaves: number;
  };
  
  // 自定义属性
  customProperties: Record<string, any>;
}

// 粒子系统定义接口
export interface ParticleSystemDefinition {
  id: string;
  properties: ParticleProperties;
  shaderCode?: {
    vertex?: string;
    fragment?: string;
    compute?: string;
  };
  metadata: ParticleMetadata;
  timestamp: number;
}

// 粒子元数据接口
export interface ParticleMetadata {
  category: string;
  tags: string[];
  performance: number; // 0-100
  quality: number; // 0-100
  complexity: number; // 0-100
  author: string;
  description: string;
  thumbnail?: string;
  previewVideo?: string;
}

// 粒子预设接口
export interface ParticlePreset {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail: string;
  previewVideo?: string;
  properties: Partial<ParticleProperties>;
  tags: string[];
  rating: number;
  downloads: number;
}

// 粒子物理模拟接口
export interface ParticlePhysics {
  enabled: boolean;
  
  // 力场
  forces: {
    gravity: [number, number, number];
    wind: [number, number, number];
    turbulence: number;
    attraction: {
      enabled: boolean;
      position: [number, number, number];
      strength: number;
      radius: number;
    };
  };
  
  // 碰撞
  colliders: {
    planes: Array<{
      position: [number, number, number];
      normal: [number, number, number];
      bounce: number;
      friction: number;
    }>;
    spheres: Array<{
      position: [number, number, number];
      radius: number;
      bounce: number;
      friction: number;
    }>;
  };
  
  // 约束
  constraints: {
    bounds: {
      enabled: boolean;
      min: [number, number, number];
      max: [number, number, number];
      killOutside: boolean;
    };
  };
}

// GPU计算缓冲区接口
export interface GPUBuffer {
  id: string;
  name: string;
  type: 'position' | 'velocity' | 'color' | 'size' | 'rotation' | 'lifetime' | 'custom';
  format: 'float32' | 'float16' | 'uint32' | 'uint16';
  size: number;
  data?: ArrayBuffer;
}

/**
 * GPU粒子系统服务类
 */
export class ParticleSystemService extends EventEmitter {
  private static instance: ParticleSystemService;
  private particleSystems: Map<string, ParticleSystemDefinition> = new Map();
  private presets: Map<string, ParticlePreset> = new Map();
  private activeSystem: ParticleSystemDefinition | null = null;
  private isSimulating: boolean = false;
  private gpuBuffers: Map<string, GPUBuffer> = new Map();

  private constructor() {
    super();
    this.initializeDefaultSystems();
    this.initializeDefaultPresets();
    this.initializeGPUBuffers();
  }

  public static getInstance(): ParticleSystemService {
    if (!ParticleSystemService.instance) {
      ParticleSystemService.instance = new ParticleSystemService();
    }
    return ParticleSystemService.instance;
  }

  /**
   * 初始化默认粒子系统
   */
  private initializeDefaultSystems(): void {
    // 火焰效果
    const fireSystem = this.createParticleSystem({
      name: 'Fire',
      type: ParticleSystemType.FIRE,
      enabled: true,
      emitterShape: EmitterShape.CONE,
      emitterSize: [1, 2, 1],
      emitterPosition: [0, 0, 0],
      emitterRotation: [0, 0, 0],
      maxParticles: 1000,
      emissionRate: 50,
      burstCount: 0,
      burstInterval: 0,
      lifetime: { min: 1, max: 3 },
      velocity: {
        initial: [0, 5, 0],
        randomness: [2, 1, 2]
      },
      size: {
        initial: 0.5,
        curve: [0.5, 1.0, 0.8, 0.0],
        randomness: 0.3
      },
      color: {
        initial: [1, 0.5, 0, 1],
        curve: [
          [1, 0.5, 0, 1],
          [1, 0.8, 0, 0.8],
          [0.8, 0.2, 0, 0.5],
          [0.2, 0, 0, 0]
        ],
        randomness: 0.2
      },
      rotation: {
        initial: 0,
        velocity: 90,
        randomness: 180
      },
      gravity: [0, -2, 0],
      drag: 0.1,
      mass: 1,
      material: {
        blendMode: BlendMode.ADDITIVE,
        sortMode: SortMode.DISTANCE,
        billboard: true,
        softParticles: true
      },
      collision: {
        enabled: false,
        bounce: 0,
        friction: 0,
        killOnCollision: false
      },
      noise: {
        enabled: true,
        strength: 0.5,
        frequency: 0.1,
        octaves: 2
      },
      customProperties: {}
    });

    // 烟雾效果
    const smokeSystem = this.createParticleSystem({
      name: 'Smoke',
      type: ParticleSystemType.SMOKE,
      enabled: true,
      emitterShape: EmitterShape.SPHERE,
      emitterSize: [0.5, 0.5, 0.5],
      emitterPosition: [0, 0, 0],
      emitterRotation: [0, 0, 0],
      maxParticles: 500,
      emissionRate: 20,
      burstCount: 0,
      burstInterval: 0,
      lifetime: { min: 3, max: 6 },
      velocity: {
        initial: [0, 2, 0],
        randomness: [1, 0.5, 1]
      },
      size: {
        initial: 0.3,
        curve: [0.3, 0.8, 1.2, 1.5],
        randomness: 0.4
      },
      color: {
        initial: [0.8, 0.8, 0.8, 0.8],
        curve: [
          [0.8, 0.8, 0.8, 0.8],
          [0.6, 0.6, 0.6, 0.6],
          [0.4, 0.4, 0.4, 0.4],
          [0.2, 0.2, 0.2, 0]
        ],
        randomness: 0.1
      },
      rotation: {
        initial: 0,
        velocity: 30,
        randomness: 60
      },
      gravity: [0, -0.5, 0],
      drag: 0.3,
      mass: 0.5,
      material: {
        blendMode: BlendMode.ALPHA,
        sortMode: SortMode.DISTANCE,
        billboard: true,
        softParticles: true
      },
      collision: {
        enabled: false,
        bounce: 0,
        friction: 0,
        killOnCollision: false
      },
      noise: {
        enabled: true,
        strength: 1.0,
        frequency: 0.05,
        octaves: 3
      },
      customProperties: {}
    });

    this.particleSystems.set(fireSystem.id, fireSystem);
    this.particleSystems.set(smokeSystem.id, smokeSystem);
  }

  /**
   * 初始化默认预设
   */
  private initializeDefaultPresets(): void {
    const presets: ParticlePreset[] = [
      {
        id: 'preset_campfire',
        name: 'Campfire',
        description: 'Cozy campfire with flames and sparks',
        category: 'fire',
        thumbnail: '/presets/campfire.jpg',
        previewVideo: '/presets/campfire.mp4',
        properties: {
          type: ParticleSystemType.FIRE,
          emissionRate: 30,
          lifetime: { min: 1.5, max: 2.5 },
          color: {
            initial: [1, 0.6, 0.2, 1],
            curve: [
              [1, 0.6, 0.2, 1],
              [1, 0.4, 0, 0.8],
              [0.8, 0.2, 0, 0.5],
              [0.2, 0, 0, 0]
            ],
            randomness: 0.3
          }
        },
        tags: ['fire', 'campfire', 'cozy'],
        rating: 4.8,
        downloads: 1520
      },
      {
        id: 'preset_explosion',
        name: 'Explosion',
        description: 'Dramatic explosion with debris',
        category: 'explosion',
        thumbnail: '/presets/explosion.jpg',
        previewVideo: '/presets/explosion.mp4',
        properties: {
          type: ParticleSystemType.EXPLOSION,
          emissionRate: 0,
          burstCount: 200,
          lifetime: { min: 0.5, max: 2 },
          velocity: {
            initial: [0, 0, 0],
            randomness: [10, 10, 10]
          }
        },
        tags: ['explosion', 'dramatic', 'debris'],
        rating: 4.9,
        downloads: 2340
      },
      {
        id: 'preset_rain',
        name: 'Rain',
        description: 'Realistic rain drops',
        category: 'weather',
        thumbnail: '/presets/rain.jpg',
        properties: {
          type: ParticleSystemType.RAIN,
          emitterShape: EmitterShape.PLANE,
          emitterSize: [20, 0, 20],
          emissionRate: 500,
          velocity: {
            initial: [0, -15, 0],
            randomness: [1, 2, 1]
          }
        },
        tags: ['rain', 'weather', 'realistic'],
        rating: 4.6,
        downloads: 890
      }
    ];

    presets.forEach(preset => {
      this.presets.set(preset.id, preset);
    });
  }

  /**
   * 初始化GPU缓冲区
   */
  private initializeGPUBuffers(): void {
    const buffers: GPUBuffer[] = [
      {
        id: 'position_buffer',
        name: 'Position Buffer',
        type: 'position',
        format: 'float32',
        size: 4 * 3 * 10000 // 10k particles, 3 floats each
      },
      {
        id: 'velocity_buffer',
        name: 'Velocity Buffer',
        type: 'velocity',
        format: 'float32',
        size: 4 * 3 * 10000
      },
      {
        id: 'color_buffer',
        name: 'Color Buffer',
        type: 'color',
        format: 'float32',
        size: 4 * 4 * 10000 // RGBA
      },
      {
        id: 'size_buffer',
        name: 'Size Buffer',
        type: 'size',
        format: 'float32',
        size: 4 * 1 * 10000
      },
      {
        id: 'lifetime_buffer',
        name: 'Lifetime Buffer',
        type: 'lifetime',
        format: 'float32',
        size: 4 * 2 * 10000 // current, max
      }
    ];

    buffers.forEach(buffer => {
      this.gpuBuffers.set(buffer.id, buffer);
    });
  }

  /**
   * 创建粒子系统
   */
  public createParticleSystem(properties: ParticleProperties): ParticleSystemDefinition {
    const systemId = `particle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const system: ParticleSystemDefinition = {
      id: systemId,
      properties,
      shaderCode: this.generateShaderCode(properties),
      metadata: {
        category: this.getCategoryForType(properties.type),
        tags: this.getTagsForType(properties.type),
        performance: this.calculatePerformance(properties),
        quality: this.calculateQuality(properties),
        complexity: this.calculateComplexity(properties),
        author: 'User',
        description: properties.name
      },
      timestamp: Date.now()
    };

    this.particleSystems.set(systemId, system);
    this.emit('particleSystemCreated', system);
    
    return system;
  }

  /**
   * 更新粒子系统
   */
  public updateParticleSystem(systemId: string, updates: Partial<ParticleProperties>): ParticleSystemDefinition {
    const system = this.particleSystems.get(systemId);
    if (!system) {
      throw new Error('Particle system not found');
    }

    system.properties = { ...system.properties, ...updates };
    system.metadata.performance = this.calculatePerformance(system.properties);
    system.metadata.quality = this.calculateQuality(system.properties);
    system.metadata.complexity = this.calculateComplexity(system.properties);
    system.timestamp = Date.now();

    // 重新生成着色器代码
    system.shaderCode = this.generateShaderCode(system.properties);

    this.emit('particleSystemUpdated', system);
    return system;
  }

  /**
   * 应用预设
   */
  public applyPreset(systemId: string, presetId: string): ParticleSystemDefinition {
    const system = this.particleSystems.get(systemId);
    const preset = this.presets.get(presetId);
    
    if (!system || !preset) {
      throw new Error('System or preset not found');
    }

    // 应用预设属性
    if (preset.properties) {
      system.properties = { ...system.properties, ...preset.properties };
      system.metadata.performance = this.calculatePerformance(system.properties);
      system.metadata.quality = this.calculateQuality(system.properties);
      system.metadata.complexity = this.calculateComplexity(system.properties);
      system.timestamp = Date.now();
      
      // 重新生成着色器代码
      system.shaderCode = this.generateShaderCode(system.properties);
    }

    this.emit('presetApplied', { system, preset });
    return system;
  }

  /**
   * 开始粒子模拟
   */
  public async startSimulation(systemId: string): Promise<void> {
    const system = this.particleSystems.get(systemId);
    if (!system) {
      throw new Error('Particle system not found');
    }

    if (this.isSimulating) {
      throw new Error('Simulation already running');
    }

    this.isSimulating = true;
    this.activeSystem = system;
    
    this.emit('simulationStarted', system);

    try {
      // 初始化GPU缓冲区
      await this.initializeSimulation(system);
      
      // 开始模拟循环
      this.simulationLoop();
      
    } catch (error) {
      this.isSimulating = false;
      this.emit('simulationError', error);
      throw error;
    }
  }

  /**
   * 停止粒子模拟
   */
  public stopSimulation(): void {
    if (!this.isSimulating) {
      return;
    }

    this.isSimulating = false;
    this.activeSystem = null;
    
    this.emit('simulationStopped');
  }

  /**
   * 初始化模拟
   */
  private async initializeSimulation(system: ParticleSystemDefinition): Promise<void> {
    // 模拟GPU缓冲区初始化
    await new Promise(resolve => setTimeout(resolve, 100));

    // 初始化粒子数据
    this.initializeParticleData(system);

    this.emit('simulationInitialized', system);
  }

  /**
   * 初始化粒子数据
   */
  private initializeParticleData(system: ParticleSystemDefinition): void {
    const { maxParticles } = system.properties;

    // 初始化位置缓冲区
    const positionBuffer = this.gpuBuffers.get('position_buffer');
    if (positionBuffer) {
      positionBuffer.data = new ArrayBuffer(4 * 3 * maxParticles);
      const positions = new Float32Array(positionBuffer.data);

      // 初始化粒子位置
      for (let i = 0; i < maxParticles * 3; i += 3) {
        positions[i] = 0;     // x
        positions[i + 1] = 0; // y
        positions[i + 2] = 0; // z
      }
    }

    // 初始化速度缓冲区
    const velocityBuffer = this.gpuBuffers.get('velocity_buffer');
    if (velocityBuffer) {
      velocityBuffer.data = new ArrayBuffer(4 * 3 * maxParticles);
      const velocities = new Float32Array(velocityBuffer.data);

      for (let i = 0; i < maxParticles * 3; i += 3) {
        velocities[i] = 0;     // vx
        velocities[i + 1] = 0; // vy
        velocities[i + 2] = 0; // vz
      }
    }

    // 初始化其他缓冲区...
    this.emit('particleDataInitialized', system);
  }

  /**
   * 模拟循环
   */
  private simulationLoop(): void {
    if (!this.isSimulating || !this.activeSystem) {
      return;
    }

    // 模拟一帧
    this.simulateFrame(this.activeSystem);

    // 继续下一帧
    requestAnimationFrame(() => this.simulationLoop());
  }

  /**
   * 模拟一帧
   */
  private simulateFrame(system: ParticleSystemDefinition): void {
    const deltaTime = 1/60; // 假设60FPS

    // 发射新粒子
    this.emitParticles(system, deltaTime);

    // 更新现有粒子
    this.updateParticles(system, deltaTime);

    // 处理碰撞
    this.handleCollisions(system);

    // 清理死亡粒子
    this.cleanupDeadParticles(system);

    this.emit('frameSimulated', system);
  }

  /**
   * 发射粒子
   */
  private emitParticles(system: ParticleSystemDefinition, deltaTime: number): void {
    const { emissionRate, burstCount, burstInterval } = system.properties;

    // 计算本帧应该发射的粒子数量
    const particlesToEmit = Math.floor(emissionRate * deltaTime);

    for (let i = 0; i < particlesToEmit; i++) {
      this.emitSingleParticle(system);
    }
  }

  /**
   * 发射单个粒子
   */
  private emitSingleParticle(system: ParticleSystemDefinition): void {
    const { emitterShape, emitterSize, velocity, lifetime, size, color } = system.properties;

    // 根据发射器形状计算初始位置
    const position = this.calculateEmissionPosition(emitterShape, emitterSize);

    // 计算初始速度
    const initialVelocity = [
      velocity.initial[0] + (Math.random() - 0.5) * velocity.randomness[0],
      velocity.initial[1] + (Math.random() - 0.5) * velocity.randomness[1],
      velocity.initial[2] + (Math.random() - 0.5) * velocity.randomness[2]
    ];

    // 计算生命周期
    const particleLifetime = lifetime.min + Math.random() * (lifetime.max - lifetime.min);

    // 创建粒子数据
    const particle = {
      position,
      velocity: initialVelocity,
      lifetime: particleLifetime,
      age: 0,
      size: size.initial + (Math.random() - 0.5) * size.randomness,
      color: [...color.initial],
      rotation: 0
    };

    this.emit('particleEmitted', particle);
  }

  /**
   * 计算发射位置
   */
  private calculateEmissionPosition(shape: EmitterShape, size: [number, number, number]): [number, number, number] {
    const [sx, sy, sz] = size;

    switch (shape) {
      case EmitterShape.POINT:
        return [0, 0, 0];

      case EmitterShape.SPHERE:
        const r = Math.random() * sx;
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.acos(2 * Math.random() - 1);
        return [
          r * Math.sin(phi) * Math.cos(theta),
          r * Math.sin(phi) * Math.sin(theta),
          r * Math.cos(phi)
        ];

      case EmitterShape.BOX:
        return [
          (Math.random() - 0.5) * sx,
          (Math.random() - 0.5) * sy,
          (Math.random() - 0.5) * sz
        ];

      case EmitterShape.CONE:
        const angle = Math.random() * Math.PI * 2;
        const radius = Math.random() * sx;
        const height = Math.random() * sy;
        return [
          radius * Math.cos(angle),
          height,
          radius * Math.sin(angle)
        ];

      default:
        return [0, 0, 0];
    }
  }

  /**
   * 更新粒子
   */
  private updateParticles(system: ParticleSystemDefinition, deltaTime: number): void {
    // 这里应该使用GPU计算着色器来更新粒子
    // 模拟GPU更新过程
    this.emit('particlesUpdated', system);
  }

  /**
   * 处理碰撞
   */
  private handleCollisions(system: ParticleSystemDefinition): void {
    if (!system.properties.collision.enabled) {
      return;
    }

    // 模拟碰撞检测和响应
    this.emit('collisionsHandled', system);
  }

  /**
   * 清理死亡粒子
   */
  private cleanupDeadParticles(system: ParticleSystemDefinition): void {
    // 模拟清理过程
    this.emit('deadParticlesCleaned', system);
  }

  /**
   * 生成着色器代码
   */
  private generateShaderCode(properties: ParticleProperties): { vertex: string; fragment: string; compute?: string } {
    const vertexShader = `
      attribute vec3 position;
      attribute vec3 velocity;
      attribute vec4 color;
      attribute float size;
      attribute float lifetime;
      attribute float age;

      uniform mat4 modelViewMatrix;
      uniform mat4 projectionMatrix;
      uniform float time;

      varying vec4 vColor;
      varying float vLifetime;

      void main() {
        vColor = color;
        vLifetime = age / lifetime;

        vec3 pos = position + velocity * time;

        gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
        gl_PointSize = size * (1.0 - vLifetime * 0.5);
      }
    `;

    const fragmentShader = `
      precision mediump float;

      uniform sampler2D particleTexture;
      uniform float opacity;

      varying vec4 vColor;
      varying float vLifetime;

      void main() {
        vec2 uv = gl_PointCoord;
        vec4 texColor = texture2D(particleTexture, uv);

        float alpha = vColor.a * (1.0 - vLifetime) * opacity;
        gl_FragColor = vec4(vColor.rgb * texColor.rgb, alpha);
      }
    `;

    const computeShader = `
      #version 450

      layout(local_size_x = 64) in;

      layout(std430, binding = 0) restrict buffer PositionBuffer {
        vec3 positions[];
      };

      layout(std430, binding = 1) restrict buffer VelocityBuffer {
        vec3 velocities[];
      };

      uniform float deltaTime;
      uniform vec3 gravity;
      uniform float drag;

      void main() {
        uint index = gl_GlobalInvocationID.x;
        if (index >= positions.length()) return;

        // Update velocity
        velocities[index] += gravity * deltaTime;
        velocities[index] *= (1.0 - drag * deltaTime);

        // Update position
        positions[index] += velocities[index] * deltaTime;
      }
    `;

    return { vertex: vertexShader, fragment: fragmentShader, compute: computeShader };
  }

  /**
   * 计算性能影响
   */
  private calculatePerformance(properties: ParticleProperties): number {
    let performance = 100;

    // 粒子数量影响
    if (properties.maxParticles > 1000) {
      performance -= 20;
    }
    if (properties.maxParticles > 5000) {
      performance -= 30;
    }

    // 复杂特性影响
    if (properties.collision.enabled) {
      performance -= 15;
    }
    if (properties.noise.enabled) {
      performance -= 10;
    }
    if (properties.material.softParticles) {
      performance -= 10;
    }

    // 发射率影响
    if (properties.emissionRate > 100) {
      performance -= 10;
    }

    return Math.max(0, performance);
  }

  /**
   * 计算质量
   */
  private calculateQuality(properties: ParticleProperties): number {
    let quality = 50;

    // 基于特性的质量提升
    if (properties.material.softParticles) {
      quality += 15;
    }
    if (properties.collision.enabled) {
      quality += 10;
    }
    if (properties.noise.enabled) {
      quality += 10;
    }
    if (properties.material.sortMode !== SortMode.NONE) {
      quality += 10;
    }

    // 粒子数量质量影响
    if (properties.maxParticles >= 1000) {
      quality += 15;
    }

    return Math.min(100, quality);
  }

  /**
   * 计算复杂度
   */
  private calculateComplexity(properties: ParticleProperties): number {
    let complexity = 20;

    // 基础复杂度
    complexity += Math.min(30, properties.maxParticles / 100);

    // 特性复杂度
    if (properties.collision.enabled) complexity += 20;
    if (properties.noise.enabled) complexity += 15;
    if (properties.material.softParticles) complexity += 10;
    if (properties.material.sortMode !== SortMode.NONE) complexity += 10;

    return Math.min(100, complexity);
  }

  /**
   * 获取类型分类
   */
  private getCategoryForType(type: ParticleSystemType): string {
    switch (type) {
      case ParticleSystemType.FIRE:
      case ParticleSystemType.SMOKE:
      case ParticleSystemType.EXPLOSION:
        return 'fire-effects';
      case ParticleSystemType.RAIN:
      case ParticleSystemType.SNOW:
        return 'weather';
      case ParticleSystemType.MAGIC:
      case ParticleSystemType.ENERGY:
        return 'magical';
      case ParticleSystemType.SPARKS:
      case ParticleSystemType.DUST:
        return 'debris';
      default:
        return 'general';
    }
  }

  /**
   * 获取类型标签
   */
  private getTagsForType(type: ParticleSystemType): string[] {
    switch (type) {
      case ParticleSystemType.FIRE:
        return ['fire', 'flame', 'heat'];
      case ParticleSystemType.SMOKE:
        return ['smoke', 'vapor', 'fog'];
      case ParticleSystemType.EXPLOSION:
        return ['explosion', 'blast', 'debris'];
      case ParticleSystemType.RAIN:
        return ['rain', 'water', 'weather'];
      case ParticleSystemType.SNOW:
        return ['snow', 'ice', 'winter'];
      case ParticleSystemType.MAGIC:
        return ['magic', 'mystical', 'fantasy'];
      default:
        return [type];
    }
  }

  /**
   * 获取所有粒子系统
   */
  public getAllParticleSystems(): ParticleSystemDefinition[] {
    return Array.from(this.particleSystems.values());
  }

  /**
   * 获取所有预设
   */
  public getAllPresets(): ParticlePreset[] {
    return Array.from(this.presets.values());
  }

  /**
   * 获取活动系统
   */
  public getActiveSystem(): ParticleSystemDefinition | null {
    return this.activeSystem;
  }

  /**
   * 删除粒子系统
   */
  public deleteParticleSystem(systemId: string): boolean {
    const system = this.particleSystems.get(systemId);
    if (!system) {
      return false;
    }

    // 如果是活动系统，先停止模拟
    if (this.activeSystem?.id === systemId) {
      this.stopSimulation();
    }

    this.particleSystems.delete(systemId);
    this.emit('particleSystemDeleted', system);
    return true;
  }

  /**
   * 克隆粒子系统
   */
  public cloneParticleSystem(systemId: string, newName?: string): ParticleSystemDefinition {
    const originalSystem = this.particleSystems.get(systemId);
    if (!originalSystem) {
      throw new Error('Particle system not found');
    }

    const clonedProperties = {
      ...originalSystem.properties,
      name: newName || `${originalSystem.properties.name} Copy`
    };

    const clonedSystem = this.createParticleSystem(clonedProperties);
    this.emit('particleSystemCloned', { original: originalSystem, cloned: clonedSystem });

    return clonedSystem;
  }

  /**
   * 导出粒子系统
   */
  public exportParticleSystem(systemId: string): string {
    const system = this.particleSystems.get(systemId);
    if (!system) {
      throw new Error('Particle system not found');
    }

    return JSON.stringify(system, null, 2);
  }

  /**
   * 导入粒子系统
   */
  public importParticleSystem(systemData: string): ParticleSystemDefinition {
    try {
      const system = JSON.parse(systemData) as ParticleSystemDefinition;
      system.id = `particle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      this.particleSystems.set(system.id, system);
      this.emit('particleSystemImported', system);

      return system;
    } catch (error) {
      throw new Error('Invalid particle system data');
    }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): {
    totalSystems: number;
    activeSystems: number;
    totalParticles: number;
    averagePerformance: number;
    memoryUsage: number;
  } {
    const systems = this.getAllParticleSystems();
    const activeSystems = systems.filter(s => s.properties.enabled);
    const totalParticles = activeSystems.reduce((sum, s) => sum + s.properties.maxParticles, 0);
    const totalPerformance = activeSystems.reduce((sum, s) => sum + s.metadata.performance, 0);
    const averagePerformance = activeSystems.length > 0 ? totalPerformance / activeSystems.length : 100;

    // 估算内存使用（MB）
    const memoryUsage = totalParticles * 0.1; // 假设每个粒子100字节

    return {
      totalSystems: systems.length,
      activeSystems: activeSystems.length,
      totalParticles,
      averagePerformance,
      memoryUsage
    };
  }

  /**
   * 检查是否正在模拟
   */
  public isSimulationRunning(): boolean {
    return this.isSimulating;
  }
}

export default ParticleSystemService;
