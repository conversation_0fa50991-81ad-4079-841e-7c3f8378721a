/**
 * 版本控制和分支管理服务
 * 提供Git风格的版本控制系统，支持分支创建、合并、冲突解决、历史回滚等功能
 */
import { EventEmitter } from '../utils/EventEmitter';

// 版本控制操作类型枚举
export enum VersionControlOperation {
  COMMIT = 'commit',
  BRANCH_CREATE = 'branch_create',
  BRANCH_DELETE = 'branch_delete',
  BRANCH_SWITCH = 'branch_switch',
  MERGE = 'merge',
  REVERT = 'revert',
  RESET = 'reset',
  TAG = 'tag',
  STASH = 'stash',
  CHERRY_PICK = 'cherry_pick'
}

// 文件状态枚举
export enum FileChangeStatus {
  ADDED = 'added',
  MODIFIED = 'modified',
  DELETED = 'deleted',
  RENAMED = 'renamed',
  COPIED = 'copied',
  UNTRACKED = 'untracked',
  IGNORED = 'ignored'
}

// 合并状态枚举
export enum MergeStatus {
  CLEAN = 'clean',
  CONFLICTED = 'conflicted',
  ABORTED = 'aborted',
  RESOLVED = 'resolved'
}

// 分支类型枚举
export enum BranchType {
  MAIN = 'main',
  FEATURE = 'feature',
  HOTFIX = 'hotfix',
  RELEASE = 'release',
  DEVELOP = 'develop',
  CUSTOM = 'custom'
}

// 提交信息接口
export interface Commit {
  id: string;
  hash: string;
  message: string;
  author: {
    name: string;
    email: string;
  };
  timestamp: number;
  parentHashes: string[];
  changes: FileChange[];
  tags: string[];
  metadata: {
    branch: string;
    isMerge: boolean;
    mergeParents?: string[];
    stats: {
      additions: number;
      deletions: number;
      filesChanged: number;
    };
  };
}

// 文件变更接口
export interface FileChange {
  id: string;
  path: string;
  oldPath?: string;
  status: FileChangeStatus;
  additions: number;
  deletions: number;
  binary: boolean;
  diff?: string;
  content?: {
    old?: string;
    new?: string;
  };
}

// 分支信息接口
export interface Branch {
  id: string;
  name: string;
  type: BranchType;
  description?: string;
  
  // 分支状态
  isActive: boolean;
  isRemote: boolean;
  isProtected: boolean;
  
  // 提交信息
  headCommit: string;
  baseCommit?: string;
  commitCount: number;
  
  // 分支关系
  upstream?: string;
  tracking?: string;
  
  // 时间信息
  createdAt: number;
  updatedAt: number;
  lastCommitAt: number;
  
  // 作者信息
  author: {
    name: string;
    email: string;
  };
  
  // 分支统计
  stats: {
    ahead: number;
    behind: number;
    commits: number;
    contributors: number;
  };
}

// 合并冲突接口
export interface MergeConflict {
  id: string;
  filePath: string;
  type: 'content' | 'rename' | 'delete' | 'modify';
  
  // 冲突内容
  conflictMarkers: {
    start: number;
    middle: number;
    end: number;
  };
  
  // 冲突版本
  current: {
    branch: string;
    content: string;
    hash: string;
  };
  
  incoming: {
    branch: string;
    content: string;
    hash: string;
  };
  
  // 解决状态
  isResolved: boolean;
  resolution?: {
    content: string;
    resolvedBy: string;
    resolvedAt: number;
    strategy: 'manual' | 'ours' | 'theirs' | 'merge';
  };
}

// 标签信息接口
export interface Tag {
  id: string;
  name: string;
  message?: string;
  commitHash: string;
  type: 'lightweight' | 'annotated';
  
  // 标签信息
  author?: {
    name: string;
    email: string;
  };
  
  createdAt: number;
  
  // 标签元数据
  metadata: {
    version?: string;
    release?: boolean;
    prerelease?: boolean;
    notes?: string;
  };
}

// 暂存信息接口
export interface Stash {
  id: string;
  index: number;
  message: string;
  branch: string;
  
  // 暂存内容
  changes: FileChange[];
  
  // 时间信息
  createdAt: number;
  
  // 作者信息
  author: {
    name: string;
    email: string;
  };
}

// 版本控制仓库接口
export interface Repository {
  id: string;
  name: string;
  description: string;
  path: string;
  
  // 仓库状态
  isInitialized: boolean;
  isBare: boolean;
  hasUncommittedChanges: boolean;
  
  // 当前状态
  currentBranch: string;
  headCommit: string;
  
  // 分支和提交
  branches: Branch[];
  commits: Commit[];
  tags: Tag[];
  stashes: Stash[];
  
  // 配置信息
  config: {
    user: {
      name: string;
      email: string;
    };
    remote?: {
      origin?: string;
      upstream?: string;
    };
    hooks: {
      preCommit?: string;
      postCommit?: string;
      prePush?: string;
      postPush?: string;
    };
  };
  
  // 统计信息
  stats: {
    totalCommits: number;
    totalBranches: number;
    totalTags: number;
    contributors: number;
    lastActivity: number;
  };
}

/**
 * 版本控制和分支管理服务类
 */
export class VersionControlService extends EventEmitter {
  private static instance: VersionControlService;
  private repositories: Map<string, Repository> = new Map();
  private activeRepository: Repository | null = null;
  private workingDirectory: Map<string, FileChange> = new Map();
  private stagingArea: Map<string, FileChange> = new Map();

  private constructor() {
    super();
  }

  public static getInstance(): VersionControlService {
    if (!VersionControlService.instance) {
      VersionControlService.instance = new VersionControlService();
    }
    return VersionControlService.instance;
  }

  /**
   * 初始化仓库
   */
  public async initializeRepository(
    name: string,
    description: string,
    path: string,
    config?: {
      user: { name: string; email: string };
      initialBranch?: string;
    }
  ): Promise<Repository> {
    const repoId = `repo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const repository: Repository = {
      id: repoId,
      name,
      description,
      path,
      isInitialized: true,
      isBare: false,
      hasUncommittedChanges: false,
      currentBranch: config?.initialBranch || 'main',
      headCommit: '',
      branches: [],
      commits: [],
      tags: [],
      stashes: [],
      config: {
        user: config?.user || { name: 'User', email: '<EMAIL>' },
        hooks: {}
      },
      stats: {
        totalCommits: 0,
        totalBranches: 1,
        totalTags: 0,
        contributors: 1,
        lastActivity: Date.now()
      }
    };

    // 创建初始分支
    const mainBranch = this.createBranch(
      config?.initialBranch || 'main',
      BranchType.MAIN,
      'Initial branch'
    );
    mainBranch.isActive = true;
    repository.branches.push(mainBranch);

    this.repositories.set(repoId, repository);
    this.activeRepository = repository;

    this.emit('repositoryInitialized', repository);
    return repository;
  }

  /**
   * 打开仓库
   */
  public async openRepository(repositoryId: string): Promise<Repository> {
    const repository = this.repositories.get(repositoryId);
    if (!repository) {
      throw new Error('Repository not found');
    }

    this.activeRepository = repository;
    this.emit('repositoryOpened', repository);
    
    return repository;
  }

  /**
   * 创建分支
   */
  public createBranch(
    name: string,
    type: BranchType = BranchType.FEATURE,
    description?: string,
    baseBranch?: string
  ): Branch {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const branchId = `branch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const baseCommit = baseBranch ? 
      this.getBranchByName(baseBranch)?.headCommit : 
      this.activeRepository.headCommit;

    const branch: Branch = {
      id: branchId,
      name,
      type,
      description,
      isActive: false,
      isRemote: false,
      isProtected: type === BranchType.MAIN,
      headCommit: baseCommit || '',
      baseCommit: baseCommit,
      commitCount: 0,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      lastCommitAt: Date.now(),
      author: this.activeRepository.config.user,
      stats: {
        ahead: 0,
        behind: 0,
        commits: 0,
        contributors: 1
      }
    };

    this.activeRepository.branches.push(branch);
    this.activeRepository.stats.totalBranches++;
    this.activeRepository.stats.lastActivity = Date.now();

    this.emit('branchCreated', branch);
    return branch;
  }

  /**
   * 切换分支
   */
  public async switchBranch(branchName: string): Promise<void> {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const targetBranch = this.getBranchByName(branchName);
    if (!targetBranch) {
      throw new Error('Branch not found');
    }

    // 检查是否有未提交的更改
    if (this.activeRepository.hasUncommittedChanges) {
      throw new Error('Cannot switch branch with uncommitted changes');
    }

    // 切换分支
    const currentBranch = this.getCurrentBranch();
    if (currentBranch) {
      currentBranch.isActive = false;
    }

    targetBranch.isActive = true;
    this.activeRepository.currentBranch = branchName;
    this.activeRepository.headCommit = targetBranch.headCommit;

    this.emit('branchSwitched', { from: currentBranch?.name, to: branchName });
  }

  /**
   * 删除分支
   */
  public async deleteBranch(branchName: string, force: boolean = false): Promise<void> {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const branch = this.getBranchByName(branchName);
    if (!branch) {
      throw new Error('Branch not found');
    }

    if (branch.isActive) {
      throw new Error('Cannot delete active branch');
    }

    if (branch.isProtected && !force) {
      throw new Error('Cannot delete protected branch');
    }

    // 删除分支
    const branchIndex = this.activeRepository.branches.findIndex(b => b.name === branchName);
    if (branchIndex !== -1) {
      this.activeRepository.branches.splice(branchIndex, 1);
      this.activeRepository.stats.totalBranches--;
    }

    this.emit('branchDeleted', branch);
  }

  /**
   * 提交更改
   */
  public async commit(message: string, options?: {
    author?: { name: string; email: string };
    allowEmpty?: boolean;
    amend?: boolean;
  }): Promise<Commit> {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    if (this.stagingArea.size === 0 && !options?.allowEmpty) {
      throw new Error('No changes staged for commit');
    }

    const commitId = `commit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const commitHash = this.generateCommitHash(commitId, message);
    
    const changes = Array.from(this.stagingArea.values());
    const stats = this.calculateCommitStats(changes);

    const commit: Commit = {
      id: commitId,
      hash: commitHash,
      message,
      author: options?.author || this.activeRepository.config.user,
      timestamp: Date.now(),
      parentHashes: this.activeRepository.headCommit ? [this.activeRepository.headCommit] : [],
      changes,
      tags: [],
      metadata: {
        branch: this.activeRepository.currentBranch,
        isMerge: false,
        stats
      }
    };

    // 更新仓库状态
    this.activeRepository.commits.unshift(commit);
    this.activeRepository.headCommit = commitHash;
    this.activeRepository.hasUncommittedChanges = false;
    this.activeRepository.stats.totalCommits++;
    this.activeRepository.stats.lastActivity = Date.now();

    // 更新当前分支
    const currentBranch = this.getCurrentBranch();
    if (currentBranch) {
      currentBranch.headCommit = commitHash;
      currentBranch.commitCount++;
      currentBranch.lastCommitAt = Date.now();
      currentBranch.stats.commits++;
    }

    // 清空暂存区
    this.stagingArea.clear();

    this.emit('commitCreated', commit);
    return commit;
  }

  /**
   * 合并分支
   */
  public async mergeBranch(
    sourceBranch: string,
    targetBranch?: string,
    options?: {
      strategy?: 'merge' | 'squash' | 'rebase';
      message?: string;
      noFastForward?: boolean;
    }
  ): Promise<{ commit?: Commit; conflicts?: MergeConflict[] }> {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const source = this.getBranchByName(sourceBranch);
    const target = targetBranch ? this.getBranchByName(targetBranch) : this.getCurrentBranch();

    if (!source || !target) {
      throw new Error('Source or target branch not found');
    }

    // 检测冲突
    const conflicts = await this.detectMergeConflicts(source, target);

    if (conflicts.length > 0) {
      this.emit('mergeConflicts', { source: sourceBranch, target: target.name, conflicts });
      return { conflicts };
    }

    // 执行合并
    const mergeCommit = await this.performMerge(source, target, options);

    this.emit('branchMerged', { source: sourceBranch, target: target.name, commit: mergeCommit });
    return { commit: mergeCommit };
  }

  /**
   * 检测合并冲突
   */
  private async detectMergeConflicts(source: Branch, target: Branch): Promise<MergeConflict[]> {
    const conflicts: MergeConflict[] = [];

    // 获取两个分支的差异
    const sourceCommits = this.getCommitsSince(source.baseCommit || '', source.headCommit);
    const targetCommits = this.getCommitsSince(target.baseCommit || '', target.headCommit);

    // 检查文件级冲突
    const sourceFiles = new Map<string, FileChange>();
    const targetFiles = new Map<string, FileChange>();

    sourceCommits.forEach(commit => {
      commit.changes.forEach(change => {
        sourceFiles.set(change.path, change);
      });
    });

    targetCommits.forEach(commit => {
      commit.changes.forEach(change => {
        targetFiles.set(change.path, change);
      });
    });

    // 查找冲突文件
    for (const [filePath, sourceChange] of sourceFiles.entries()) {
      const targetChange = targetFiles.get(filePath);

      if (targetChange && this.hasContentConflict(sourceChange, targetChange)) {
        const conflict: MergeConflict = {
          id: `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          filePath,
          type: 'content',
          conflictMarkers: { start: 0, middle: 0, end: 0 },
          current: {
            branch: target.name,
            content: targetChange.content?.new || '',
            hash: target.headCommit
          },
          incoming: {
            branch: source.name,
            content: sourceChange.content?.new || '',
            hash: source.headCommit
          },
          isResolved: false
        };

        conflicts.push(conflict);
      }
    }

    return conflicts;
  }

  /**
   * 检查内容冲突
   */
  private hasContentConflict(change1: FileChange, change2: FileChange): boolean {
    // 简化的冲突检测逻辑
    return change1.status === FileChangeStatus.MODIFIED &&
           change2.status === FileChangeStatus.MODIFIED &&
           change1.content?.new !== change2.content?.new;
  }

  /**
   * 执行合并
   */
  private async performMerge(
    source: Branch,
    target: Branch,
    options?: any
  ): Promise<Commit> {
    const mergeMessage = options?.message || `Merge branch '${source.name}' into '${target.name}'`;

    const mergeCommit: Commit = {
      id: `merge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      hash: this.generateCommitHash('merge', mergeMessage),
      message: mergeMessage,
      author: this.activeRepository!.config.user,
      timestamp: Date.now(),
      parentHashes: [target.headCommit, source.headCommit],
      changes: [],
      tags: [],
      metadata: {
        branch: target.name,
        isMerge: true,
        mergeParents: [target.headCommit, source.headCommit],
        stats: { additions: 0, deletions: 0, filesChanged: 0 }
      }
    };

    // 更新目标分支
    target.headCommit = mergeCommit.hash;
    target.commitCount++;
    target.lastCommitAt = Date.now();

    // 添加提交到仓库
    this.activeRepository!.commits.unshift(mergeCommit);
    this.activeRepository!.headCommit = mergeCommit.hash;
    this.activeRepository!.stats.totalCommits++;

    return mergeCommit;
  }

  /**
   * 解决合并冲突
   */
  public async resolveConflict(
    conflictId: string,
    resolution: {
      strategy: 'manual' | 'ours' | 'theirs' | 'merge';
      content?: string;
    }
  ): Promise<void> {
    // 这里应该实现冲突解决逻辑
    this.emit('conflictResolved', { conflictId, resolution });
  }

  /**
   * 回滚到指定提交
   */
  public async revertToCommit(commitHash: string, options?: {
    hard?: boolean;
    createCommit?: boolean;
  }): Promise<void> {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const commit = this.getCommitByHash(commitHash);
    if (!commit) {
      throw new Error('Commit not found');
    }

    if (options?.hard) {
      // 硬重置：丢弃所有更改
      this.activeRepository.headCommit = commitHash;
      this.workingDirectory.clear();
      this.stagingArea.clear();
      this.activeRepository.hasUncommittedChanges = false;
    }

    if (options?.createCommit) {
      // 创建回滚提交
      const revertCommit = await this.commit(`Revert to ${commitHash.substr(0, 8)}`, {
        allowEmpty: true
      });
      this.emit('commitReverted', { originalCommit: commit, revertCommit });
    }

    this.emit('repositoryReverted', { commit, options });
  }

  /**
   * 创建标签
   */
  public createTag(
    name: string,
    commitHash?: string,
    options?: {
      message?: string;
      annotated?: boolean;
      metadata?: any;
    }
  ): Tag {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const tagId = `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const targetCommit = commitHash || this.activeRepository.headCommit;

    const tag: Tag = {
      id: tagId,
      name,
      message: options?.message,
      commitHash: targetCommit,
      type: options?.annotated ? 'annotated' : 'lightweight',
      author: options?.annotated ? this.activeRepository.config.user : undefined,
      createdAt: Date.now(),
      metadata: options?.metadata || {}
    };

    this.activeRepository.tags.push(tag);
    this.activeRepository.stats.totalTags++;

    // 将标签添加到对应的提交
    const commit = this.getCommitByHash(targetCommit);
    if (commit) {
      commit.tags.push(name);
    }

    this.emit('tagCreated', tag);
    return tag;
  }

  /**
   * 删除标签
   */
  public deleteTag(tagName: string): boolean {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const tagIndex = this.activeRepository.tags.findIndex(t => t.name === tagName);
    if (tagIndex === -1) {
      return false;
    }

    const tag = this.activeRepository.tags[tagIndex];
    this.activeRepository.tags.splice(tagIndex, 1);
    this.activeRepository.stats.totalTags--;

    // 从提交中移除标签
    const commit = this.getCommitByHash(tag.commitHash);
    if (commit) {
      const tagIdx = commit.tags.indexOf(tagName);
      if (tagIdx !== -1) {
        commit.tags.splice(tagIdx, 1);
      }
    }

    this.emit('tagDeleted', tag);
    return true;
  }

  /**
   * 暂存更改
   */
  public stashChanges(message?: string): Stash {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const stashId = `stash_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const changes = Array.from(this.workingDirectory.values());

    const stash: Stash = {
      id: stashId,
      index: this.activeRepository.stashes.length,
      message: message || `WIP on ${this.activeRepository.currentBranch}`,
      branch: this.activeRepository.currentBranch,
      changes,
      createdAt: Date.now(),
      author: this.activeRepository.config.user
    };

    this.activeRepository.stashes.unshift(stash);
    this.workingDirectory.clear();
    this.activeRepository.hasUncommittedChanges = false;

    this.emit('changesStashed', stash);
    return stash;
  }

  /**
   * 应用暂存
   */
  public async applyStash(stashIndex: number, drop: boolean = false): Promise<void> {
    if (!this.activeRepository) {
      throw new Error('No active repository');
    }

    const stash = this.activeRepository.stashes[stashIndex];
    if (!stash) {
      throw new Error('Stash not found');
    }

    // 应用暂存的更改
    stash.changes.forEach(change => {
      this.workingDirectory.set(change.path, change);
    });

    this.activeRepository.hasUncommittedChanges = true;

    if (drop) {
      this.activeRepository.stashes.splice(stashIndex, 1);
      // 更新其他暂存的索引
      this.activeRepository.stashes.forEach((s, i) => {
        s.index = i;
      });
    }

    this.emit('stashApplied', { stash, dropped: drop });
  }

  /**
   * 获取提交历史
   */
  public getCommitHistory(
    branchName?: string,
    limit?: number,
    offset?: number
  ): Commit[] {
    if (!this.activeRepository) {
      return [];
    }

    let commits = this.activeRepository.commits;

    if (branchName) {
      const branch = this.getBranchByName(branchName);
      if (branch) {
        commits = this.getCommitsSince('', branch.headCommit);
      }
    }

    const start = offset || 0;
    const end = limit ? start + limit : undefined;

    return commits.slice(start, end);
  }

  /**
   * 获取文件差异
   */
  public getFileDiff(filePath: string, commitHash1?: string, commitHash2?: string): string {
    // 这里应该实现文件差异计算逻辑
    // 简化实现，返回模拟的差异
    return `--- a/${filePath}\n+++ b/${filePath}\n@@ -1,3 +1,4 @@\n line1\n-line2\n+line2 modified\n+new line\n line3`;
  }

  /**
   * 添加文件到暂存区
   */
  public stageFile(filePath: string): void {
    const change = this.workingDirectory.get(filePath);
    if (change) {
      this.stagingArea.set(filePath, change);
      this.emit('fileStaged', change);
    }
  }

  /**
   * 从暂存区移除文件
   */
  public unstageFile(filePath: string): void {
    const change = this.stagingArea.get(filePath);
    if (change) {
      this.stagingArea.delete(filePath);
      this.emit('fileUnstaged', change);
    }
  }

  /**
   * 获取工作目录状态
   */
  public getWorkingDirectoryStatus(): {
    staged: FileChange[];
    unstaged: FileChange[];
    untracked: FileChange[];
  } {
    const staged = Array.from(this.stagingArea.values());
    const unstaged = Array.from(this.workingDirectory.values())
      .filter(change => !this.stagingArea.has(change.path));
    const untracked = unstaged.filter(change => change.status === FileChangeStatus.UNTRACKED);

    return { staged, unstaged, untracked };
  }

  // ========== 辅助方法 ==========

  /**
   * 根据名称获取分支
   */
  private getBranchByName(name: string): Branch | undefined {
    return this.activeRepository?.branches.find(b => b.name === name);
  }

  /**
   * 获取当前分支
   */
  private getCurrentBranch(): Branch | undefined {
    return this.activeRepository?.branches.find(b => b.isActive);
  }

  /**
   * 根据哈希获取提交
   */
  private getCommitByHash(hash: string): Commit | undefined {
    return this.activeRepository?.commits.find(c => c.hash === hash);
  }

  /**
   * 获取指定范围的提交
   */
  private getCommitsSince(baseHash: string, headHash: string): Commit[] {
    if (!this.activeRepository) return [];

    const commits: Commit[] = [];
    let currentHash = headHash;

    while (currentHash && currentHash !== baseHash) {
      const commit = this.getCommitByHash(currentHash);
      if (!commit) break;

      commits.push(commit);
      currentHash = commit.parentHashes[0] || '';
    }

    return commits;
  }

  /**
   * 生成提交哈希
   */
  private generateCommitHash(id: string, message: string): string {
    const data = `${id}${message}${Date.now()}`;
    // 简化的哈希生成，实际应该使用SHA-1
    return btoa(data).substr(0, 40);
  }

  /**
   * 计算提交统计
   */
  private calculateCommitStats(changes: FileChange[]): { additions: number; deletions: number; filesChanged: number } {
    return changes.reduce(
      (stats, change) => ({
        additions: stats.additions + change.additions,
        deletions: stats.deletions + change.deletions,
        filesChanged: stats.filesChanged + 1
      }),
      { additions: 0, deletions: 0, filesChanged: 0 }
    );
  }

  /**
   * 获取所有仓库
   */
  public getAllRepositories(): Repository[] {
    return Array.from(this.repositories.values());
  }

  /**
   * 获取活动仓库
   */
  public getActiveRepository(): Repository | null {
    return this.activeRepository;
  }

  /**
   * 获取分支列表
   */
  public getBranches(): Branch[] {
    return this.activeRepository?.branches || [];
  }

  /**
   * 获取标签列表
   */
  public getTags(): Tag[] {
    return this.activeRepository?.tags || [];
  }

  /**
   * 获取暂存列表
   */
  public getStashes(): Stash[] {
    return this.activeRepository?.stashes || [];
  }
}

export default VersionControlService;
