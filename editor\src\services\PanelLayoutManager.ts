/**
 * 面板布局管理器
 * 负责面板布局的保存、加载、管理等功能
 */
import { LayoutData } from 'rc-dock';

export interface LayoutPreset {
  id: string;
  name: string;
  description: string;
  layout: LayoutData;
  thumbnail?: string;
  isDefault?: boolean;
  createdAt: number;
  updatedAt: number;
}

export interface UserPreferences {
  defaultLayout: string;
  autoSave: boolean;
  autoRestore: boolean;
  compactMode: boolean;
  theme: 'light' | 'dark' | 'compact';
  showToolbar: boolean;
  panelAnimations: boolean;
}

class PanelLayoutManager {
  private static instance: PanelLayoutManager;
  private readonly STORAGE_KEY_LAYOUTS = 'dl-editor-panel-layouts';
  private readonly STORAGE_KEY_PREFERENCES = 'dl-editor-panel-preferences';
  private readonly STORAGE_KEY_CURRENT = 'dl-editor-current-layout';

  private layouts: Map<string, LayoutPreset> = new Map();
  private preferences: UserPreferences;

  private constructor() {
    this.preferences = this.getDefaultPreferences();
    this.loadFromStorage();
  }

  public static getInstance(): PanelLayoutManager {
    if (!PanelLayoutManager.instance) {
      PanelLayoutManager.instance = new PanelLayoutManager();
    }
    return PanelLayoutManager.instance;
  }

  /**
   * 获取默认用户偏好设置
   */
  private getDefaultPreferences(): UserPreferences {
    return {
      defaultLayout: 'default',
      autoSave: true,
      autoRestore: true,
      compactMode: false,
      theme: 'light',
      showToolbar: true,
      panelAnimations: true
    };
  }

  /**
   * 从本地存储加载数据
   */
  private loadFromStorage(): void {
    try {
      // 加载布局预设
      const layoutsData = localStorage.getItem(this.STORAGE_KEY_LAYOUTS);
      if (layoutsData) {
        const parsedLayouts = JSON.parse(layoutsData);
        this.layouts = new Map(Object.entries(parsedLayouts));
      }

      // 加载用户偏好
      const preferencesData = localStorage.getItem(this.STORAGE_KEY_PREFERENCES);
      if (preferencesData) {
        this.preferences = { ...this.preferences, ...JSON.parse(preferencesData) };
      }
    } catch (error) {
      console.error('Failed to load panel layout data from storage:', error);
    }
  }

  /**
   * 保存数据到本地存储
   */
  private saveToStorage(): void {
    try {
      // 保存布局预设
      const layoutsObject = Object.fromEntries(this.layouts);
      localStorage.setItem(this.STORAGE_KEY_LAYOUTS, JSON.stringify(layoutsObject));

      // 保存用户偏好
      localStorage.setItem(this.STORAGE_KEY_PREFERENCES, JSON.stringify(this.preferences));
    } catch (error) {
      console.error('Failed to save panel layout data to storage:', error);
    }
  }

  /**
   * 保存布局预设
   */
  public saveLayout(id: string, name: string, layout: LayoutData, description?: string): void {
    const preset: LayoutPreset = {
      id,
      name,
      description: description || '',
      layout,
      isDefault: false,
      createdAt: this.layouts.has(id) ? this.layouts.get(id)!.createdAt : Date.now(),
      updatedAt: Date.now()
    };

    this.layouts.set(id, preset);
    this.saveToStorage();
  }

  /**
   * 加载布局预设
   */
  public loadLayout(id: string): LayoutData | null {
    const preset = this.layouts.get(id);
    return preset ? preset.layout : null;
  }

  /**
   * 删除布局预设
   */
  public deleteLayout(id: string): boolean {
    if (this.layouts.has(id)) {
      this.layouts.delete(id);
      this.saveToStorage();
      return true;
    }
    return false;
  }

  /**
   * 获取所有布局预设
   */
  public getAllLayouts(): LayoutPreset[] {
    return Array.from(this.layouts.values()).sort((a, b) => b.updatedAt - a.updatedAt);
  }

  /**
   * 获取布局预设
   */
  public getLayout(id: string): LayoutPreset | null {
    return this.layouts.get(id) || null;
  }

  /**
   * 设置默认布局
   */
  public setDefaultLayout(id: string): void {
    if (this.layouts.has(id)) {
      // 清除之前的默认布局
      this.layouts.forEach(layout => {
        layout.isDefault = false;
      });

      // 设置新的默认布局
      const layout = this.layouts.get(id)!;
      layout.isDefault = true;
      this.preferences.defaultLayout = id;
      this.saveToStorage();
    }
  }

  /**
   * 获取默认布局
   */
  public getDefaultLayout(): LayoutData | null {
    const defaultId = this.preferences.defaultLayout;
    return this.loadLayout(defaultId);
  }

  /**
   * 保存当前布局
   */
  public saveCurrentLayout(layout: LayoutData): void {
    localStorage.setItem(this.STORAGE_KEY_CURRENT, JSON.stringify(layout));
  }

  /**
   * 加载当前布局
   */
  public loadCurrentLayout(): LayoutData | null {
    try {
      const layoutData = localStorage.getItem(this.STORAGE_KEY_CURRENT);
      return layoutData ? JSON.parse(layoutData) : null;
    } catch (error) {
      console.error('Failed to load current layout:', error);
      return null;
    }
  }

  /**
   * 更新用户偏好设置
   */
  public updatePreferences(preferences: Partial<UserPreferences>): void {
    this.preferences = { ...this.preferences, ...preferences };
    this.saveToStorage();
  }

  /**
   * 获取用户偏好设置
   */
  public getPreferences(): UserPreferences {
    return { ...this.preferences };
  }

  /**
   * 重置所有设置
   */
  public reset(): void {
    this.layouts.clear();
    this.preferences = this.getDefaultPreferences();
    localStorage.removeItem(this.STORAGE_KEY_LAYOUTS);
    localStorage.removeItem(this.STORAGE_KEY_PREFERENCES);
    localStorage.removeItem(this.STORAGE_KEY_CURRENT);
  }

  /**
   * 导出布局配置
   */
  public exportLayouts(): string {
    const data = {
      layouts: Object.fromEntries(this.layouts),
      preferences: this.preferences,
      exportedAt: Date.now()
    };
    return JSON.stringify(data, null, 2);
  }

  /**
   * 导入布局配置
   */
  public importLayouts(data: string): boolean {
    try {
      const parsed = JSON.parse(data);
      if (parsed.layouts) {
        this.layouts = new Map(Object.entries(parsed.layouts));
      }
      if (parsed.preferences) {
        this.preferences = { ...this.preferences, ...parsed.preferences };
      }
      this.saveToStorage();
      return true;
    } catch (error) {
      console.error('Failed to import layouts:', error);
      return false;
    }
  }

  /**
   * 创建布局缩略图
   */
  public async createThumbnail(layout: LayoutData): Promise<string | null> {
    // 这里可以实现布局缩略图生成逻辑
    // 例如使用canvas或者截图API
    return null;
  }
}

export default PanelLayoutManager;
