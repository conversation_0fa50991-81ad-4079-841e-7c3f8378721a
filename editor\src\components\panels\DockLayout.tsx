/**
 * 基于rc-dock的可停靠面板布局系统
 */
import React, { useRef, useEffect, useState, useCallback } from 'react';
import { DockLayout as RcDockLayout, LayoutData, TabData, PanelData, BoxData } from 'rc-dock';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { Button, Dropdown, Menu, Tooltip } from 'antd';
import {
  SettingOutlined,
  LayoutOutlined,
  SaveOutlined,
  ReloadOutlined,
  PlusOutlined,
  CloseOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { PanelType } from '../../store/ui/uiSlice';
import { createPanelComponent, createPanelTitle } from './PanelRegistry';
import { savePanelLayout, loadPanelLayout } from '../../store/ui/uiSlice';
import PanelLayoutManager from '../../services/PanelLayoutManager';
import PanelLayoutSettings from './PanelLayoutSettings';
import ThemeSelector from './ThemeSelector';
import PluginManager from './PluginManager';
import './DockLayout.less';

// 创建默认面板布局配置的函数
const createDefaultLayout = (): LayoutData => {
  return {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 250,
          children: [
            {
              tabs: [
                {
                  id: 'hierarchy',
                  title: 'Hierarchy',
                  content: <div>Hierarchy Panel</div>,
                  closable: false
                },
                {
                  id: 'assets',
                  title: 'Assets',
                  content: <div>Assets Panel</div>,
                  closable: false
                }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          children: [
            {
              size: 400,
              tabs: [
                {
                  id: 'viewport',
                  title: 'Viewport',
                  content: <div>Viewport</div>,
                  closable: false
                }
              ]
            },
            {
              size: 200,
              tabs: [
                {
                  id: 'console',
                  title: 'Console',
                  content: <div>Console Panel</div>,
                  closable: true
                }
              ]
            }
          ]
        },
        {
          mode: 'vertical',
          size: 300,
          children: [
            {
              tabs: [
                {
                  id: 'inspector',
                  title: 'Inspector',
                  content: <div>Inspector Panel</div>,
                  closable: false
                }
              ]
            }
          ]
        }
      ]
    }
  };
};

// 面板配置接口
interface PanelConfig {
  id: string;
  type: PanelType;
  title: string;
  closable: boolean;
  icon?: React.ReactNode;
  component?: React.ComponentType<any>;
  props?: any;
}

// 可用面板列表
const availablePanels: PanelConfig[] = [
  { id: 'hierarchy', type: PanelType.HIERARCHY, title: 'Hierarchy', closable: false },
  { id: 'inspector', type: PanelType.INSPECTOR, title: 'Inspector', closable: false },
  { id: 'assets', type: PanelType.ASSETS, title: 'Assets', closable: false },
  { id: 'scene', type: PanelType.SCENE, title: 'Scene', closable: false },
  { id: 'console', type: PanelType.CONSOLE, title: 'Console', closable: true },
  { id: 'layers', type: PanelType.LAYERS, title: 'Layers', closable: true },
  { id: 'collaboration', type: PanelType.COLLABORATION, title: 'Collaboration', closable: true },
  { id: 'debug', type: PanelType.DEBUG, title: 'Debug', closable: true },
  { id: 'performance', type: PanelType.PERFORMANCE_OPTIMIZATION, title: 'Performance', closable: true }
];

interface DockLayoutProps {
  className?: string;
  onLayoutChange?: (layout: LayoutData) => void;
}

const DockLayout: React.FC<DockLayoutProps> = ({ className, onLayoutChange }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const dockRef = useRef<RcDockLayout>(null);
  const [layout, setLayout] = useState<LayoutData>(createDefaultLayout());
  const [availablePanelList, setAvailablePanelList] = useState(availablePanels);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [pluginManagerVisible, setPluginManagerVisible] = useState(false);

  const layoutManager = PanelLayoutManager.getInstance();
  
  // 从Redux store加载保存的布局
  const savedLayout = useSelector((state: RootState) => state.ui.panelLayout);

  useEffect(() => {
    if (savedLayout) {
      setLayout(savedLayout);
    } else {
      // 尝试从布局管理器加载
      const currentLayout = layoutManager.loadCurrentLayout();
      const defaultLayout = layoutManager.getDefaultLayout();

      if (currentLayout) {
        setLayout(currentLayout);
      } else if (defaultLayout) {
        setLayout(defaultLayout);
      }
    }
  }, [savedLayout, layoutManager]);

  // 创建面板内容
  const createPanelContent = useCallback((panelConfig: PanelConfig) => {
    const PanelComponent = createPanelComponent(panelConfig.type);
    if (PanelComponent) {
      return <PanelComponent {...(panelConfig.props || {})} />;
    }
    return <div>Panel not found: {panelConfig.type}</div>;
  }, []);

  // 创建Tab数据
  const createTabData = useCallback((panelConfig: PanelConfig): TabData => {
    return {
      id: panelConfig.id,
      title: createPanelTitle(panelConfig.type),
      content: createPanelContent(panelConfig),
      closable: panelConfig.closable,
      cached: true
    };
  }, [createPanelContent]);

  // 处理布局变化
  const handleLayoutChange = useCallback((newLayout: LayoutData) => {
    setLayout(newLayout);
    dispatch(savePanelLayout(newLayout));

    // 保存到布局管理器
    const preferences = layoutManager.getPreferences();
    if (preferences.autoSave) {
      layoutManager.saveCurrentLayout(newLayout);
    }

    onLayoutChange?.(newLayout);
  }, [dispatch, onLayoutChange, layoutManager]);

  // 添加面板
  const addPanel = useCallback((panelConfig: PanelConfig) => {
    if (!dockRef.current) return;
    
    const tabData = createTabData(panelConfig);
    dockRef.current.dockMove(tabData, null, 'middle');
  }, [createTabData]);

  // 保存当前布局
  const saveLayout = useCallback(() => {
    if (dockRef.current) {
      const currentLayout = dockRef.current.getLayout();
      dispatch(savePanelLayout(currentLayout));
      layoutManager.saveCurrentLayout(currentLayout);
    }
  }, [dispatch, layoutManager]);

  // 重置布局
  const resetLayout = useCallback(() => {
    const newLayout = createDefaultLayout();
    setLayout(newLayout);
    dispatch(savePanelLayout(newLayout));
  }, [dispatch]);

  // 处理布局加载
  const handleLayoutLoad = useCallback((newLayout: LayoutData) => {
    setLayout(newLayout);
    dispatch(savePanelLayout(newLayout));
  }, [dispatch]);

  // 添加面板菜单项
  const addPanelMenuItems = availablePanelList.map(panel => ({
    key: panel.id,
    label: panel.title,
    onClick: () => addPanel(panel)
  }));

  return (
    <div className={`dock-layout-container ${className || ''}`}>
      {/* 工具栏 */}
      <div className="dock-toolbar">
        <div className="dock-toolbar-left">
          <Tooltip title={t('editor.panels.addPanel')}>
            <Dropdown menu={{ items: addPanelMenuItems }} trigger={['click']}>
              <Button type="text" icon={<PlusOutlined />} size="small">
                {t('editor.panels.addPanel')}
              </Button>
            </Dropdown>
          </Tooltip>

          <Tooltip title={t('editor.panels.pluginManager')}>
            <Button
              type="text"
              icon={<AppstoreOutlined />}
              size="small"
              onClick={() => setPluginManagerVisible(true)}
            >
              {t('editor.panels.plugins')}
            </Button>
          </Tooltip>
        </div>
        
        <div className="dock-toolbar-right">
          <ThemeSelector size="small" showCustomizer={true} />

          <Tooltip title={t('editor.panels.saveLayout')}>
            <Button
              type="text"
              icon={<SaveOutlined />}
              size="small"
              onClick={saveLayout}
            />
          </Tooltip>

          <Tooltip title={t('editor.panels.resetLayout')}>
            <Button
              type="text"
              icon={<ReloadOutlined />}
              size="small"
              onClick={resetLayout}
            />
          </Tooltip>

          <Tooltip title={t('editor.panels.layoutSettings')}>
            <Button
              type="text"
              icon={<SettingOutlined />}
              size="small"
              onClick={() => setSettingsVisible(true)}
            />
          </Tooltip>
        </div>
      </div>

      {/* Dock布局 */}
      <RcDockLayout
        ref={dockRef}
        layout={layout}
        onLayoutChange={handleLayoutChange}
        style={{ height: 'calc(100% - 40px)' }}
      />

      {/* 布局设置对话框 */}
      <PanelLayoutSettings
        visible={settingsVisible}
        onClose={() => setSettingsVisible(false)}
        currentLayout={layout}
        onLayoutLoad={handleLayoutLoad}
      />

      {/* 插件管理器 */}
      <PluginManager
        visible={pluginManagerVisible}
        onClose={() => setPluginManagerVisible(false)}
      />
    </div>
  );
};

export default DockLayout;
