/**
 * 优化的节点注册系统
 * 统一管理所有413个视觉脚本节点的注册
 */
import { NodeRegistry } from '../nodes/NodeRegistry';

// 导入所有节点注册函数
import { registerCoreNodes } from './CoreNodes';
import { registerMathNodes } from './MathNodes';
import { registerDebugNodes } from './DebugNodes';
import { registerAINodes } from './AINodes';

/**
 * 节点注册配置
 */
export interface NodeRegistrationConfig {
  useOptimizedNodes: boolean;
  debugMode: boolean;
  enabledCategories?: string[];
  disabledNodes?: string[];
}

/**
 * 节点注册统计
 */
export interface NodeRegistrationStats {
  totalNodes: number;
  coreNodes: number;
  mathNodes: number;
  debugNodes: number;
  aiNodes: number;
  networkNodes: number;
  uiNodes: number;
  multimediaNodes: number;
  specializedNodes: number;
  registrationTime: number;
}

/**
 * 节点注册验证结果
 */
export interface NodeRegistrationValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  stats: NodeRegistrationStats;
}

/**
 * 注册所有节点到注册表
 * @param registry 节点注册表
 * @param config 注册配置
 */
export function registerAllNodes(registry: NodeRegistry, config: NodeRegistrationConfig = { useOptimizedNodes: true, debugMode: false }): NodeRegistrationStats {
  const startTime = performance.now();
  
  console.log('🚀 开始注册视觉脚本节点...');
  
  let stats: NodeRegistrationStats = {
    totalNodes: 0,
    coreNodes: 0,
    mathNodes: 0,
    debugNodes: 0,
    aiNodes: 0,
    networkNodes: 0,
    uiNodes: 0,
    multimediaNodes: 0,
    specializedNodes: 0,
    registrationTime: 0
  };

  try {
    // 1. 核心节点 (14个)
    if (shouldRegisterCategory('core', config)) {
      const beforeCount = registry.getRegisteredNodeCount();
      registerCoreNodes(registry);
      stats.coreNodes = registry.getRegisteredNodeCount() - beforeCount;
      console.log(`✅ 核心节点注册完成: ${stats.coreNodes}个`);
    }

    // 2. 数学节点 (16个)
    if (shouldRegisterCategory('math', config)) {
      const beforeCount = registry.getRegisteredNodeCount();
      registerMathNodes(registry);
      stats.mathNodes = registry.getRegisteredNodeCount() - beforeCount;
      console.log(`✅ 数学节点注册完成: ${stats.mathNodes}个`);
    }

    // 3. 调试节点 (9个)
    if (shouldRegisterCategory('debug', config)) {
      const beforeCount = registry.getRegisteredNodeCount();
      registerDebugNodes(registry);
      stats.debugNodes = registry.getRegisteredNodeCount() - beforeCount;
      console.log(`✅ 调试节点注册完成: ${stats.debugNodes}个`);
    }

    // 4. AI节点 (46个)
    if (shouldRegisterCategory('ai', config)) {
      const beforeCount = registry.getRegisteredNodeCount();
      registerAINodes(registry);
      stats.aiNodes = registry.getRegisteredNodeCount() - beforeCount;
      console.log(`✅ AI节点注册完成: ${stats.aiNodes}个`);
    }

    // 5. 网络节点 (43个) - 待实现
    if (shouldRegisterCategory('network', config)) {
      stats.networkNodes = 0; // 暂时为0，待实现
      console.log(`⏳ 网络节点待实现: ${stats.networkNodes}个`);
    }

    // 6. UI节点 (34个) - 待实现
    if (shouldRegisterCategory('ui', config)) {
      stats.uiNodes = 0; // 暂时为0，待实现
      console.log(`⏳ UI节点待实现: ${stats.uiNodes}个`);
    }

    // 7. 多媒体节点 (62个) - 待实现
    if (shouldRegisterCategory('multimedia', config)) {
      stats.multimediaNodes = 0; // 暂时为0，待实现
      console.log(`⏳ 多媒体节点待实现: ${stats.multimediaNodes}个`);
    }

    // 8. 专业应用节点 (119个) - 待实现
    if (shouldRegisterCategory('specialized', config)) {
      stats.specializedNodes = 0; // 暂时为0，待实现
      console.log(`⏳ 专业应用节点待实现: ${stats.specializedNodes}个`);
    }

    // 计算总数和注册时间
    stats.totalNodes = registry.getRegisteredNodeCount();
    stats.registrationTime = performance.now() - startTime;

    console.log(`🎉 节点注册完成! 总计: ${stats.totalNodes}个节点，耗时: ${stats.registrationTime.toFixed(2)}ms`);

  } catch (error: any) {
    console.error('❌ 节点注册失败:', error);
    throw error;
  }

  return stats;
}

/**
 * 检查是否应该注册指定类别的节点
 */
function shouldRegisterCategory(category: string, config: NodeRegistrationConfig): boolean {
  // 如果指定了启用的类别，只注册指定的类别
  if (config.enabledCategories && config.enabledCategories.length > 0) {
    return config.enabledCategories.includes(category);
  }
  
  // 默认注册所有类别
  return true;
}

/**
 * 获取节点注册统计信息
 */
export function getNodeRegistrationStats(registry: NodeRegistry): NodeRegistrationStats {
  return {
    totalNodes: registry.getRegisteredNodeCount(),
    coreNodes: registry.getNodesByCategory('core').length,
    mathNodes: registry.getNodesByCategory('math').length,
    debugNodes: registry.getNodesByCategory('debug').length,
    aiNodes: registry.getNodesByCategory('ai').length,
    networkNodes: registry.getNodesByCategory('network').length,
    uiNodes: registry.getNodesByCategory('ui').length,
    multimediaNodes: registry.getNodesByCategory('multimedia').length,
    specializedNodes: registry.getNodesByCategory('specialized').length,
    registrationTime: 0
  };
}

/**
 * 验证节点注册完整性
 */
export function validateNodeRegistration(registry: NodeRegistry): NodeRegistrationValidation {
  const errors: string[] = [];
  const warnings: string[] = [];
  const stats = getNodeRegistrationStats(registry);

  // 检查核心节点
  if (stats.coreNodes === 0) {
    errors.push('核心节点未注册');
  } else if (stats.coreNodes < 10) {
    warnings.push(`核心节点数量较少: ${stats.coreNodes}个`);
  }

  // 检查数学节点
  if (stats.mathNodes === 0) {
    warnings.push('数学节点未注册');
  }

  // 检查AI节点
  if (stats.aiNodes === 0) {
    warnings.push('AI节点未注册');
  }

  // 检查总节点数
  if (stats.totalNodes < 50) {
    warnings.push(`节点总数较少: ${stats.totalNodes}个，目标413个`);
  }

  // 检查节点类型覆盖
  const expectedCategories = ['core', 'math', 'debug', 'ai', 'network', 'ui', 'multimedia', 'specialized'];
  const registeredCategories = registry.getRegisteredCategories();
  
  for (const category of expectedCategories) {
    if (!registeredCategories.includes(category)) {
      warnings.push(`缺少节点类别: ${category}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    stats
  };
}

/**
 * 打印节点注册报告
 */
export function printNodeRegistrationReport(registry: NodeRegistry): void {
  const validation = validateNodeRegistration(registry);
  const stats = validation.stats;

  console.log('\n📊 节点注册报告');
  console.log('================');
  console.log(`总节点数: ${stats.totalNodes}`);
  console.log(`核心节点: ${stats.coreNodes}`);
  console.log(`数学节点: ${stats.mathNodes}`);
  console.log(`调试节点: ${stats.debugNodes}`);
  console.log(`AI节点: ${stats.aiNodes}`);
  console.log(`网络节点: ${stats.networkNodes}`);
  console.log(`UI节点: ${stats.uiNodes}`);
  console.log(`多媒体节点: ${stats.multimediaNodes}`);
  console.log(`专业应用节点: ${stats.specializedNodes}`);
  
  console.log('\n📈 完成度分析');
  console.log('================');
  const targetTotal = 413;
  const completionRate = (stats.totalNodes / targetTotal * 100).toFixed(1);
  console.log(`完成度: ${completionRate}% (${stats.totalNodes}/${targetTotal})`);
  
  // 各类别完成度
  const categoryTargets = {
    core: 14,
    math: 16,
    debug: 9,
    ai: 46,
    network: 43,
    ui: 34,
    multimedia: 62,
    specialized: 119
  };

  Object.entries(categoryTargets).forEach(([category, target]) => {
    const actual = (stats as any)[`${category}Nodes`];
    const rate = (actual / target * 100).toFixed(1);
    console.log(`${category}: ${rate}% (${actual}/${target})`);
  });

  // 显示错误和警告
  if (validation.errors.length > 0) {
    console.log('\n❌ 错误');
    validation.errors.forEach(error => console.log(`  - ${error}`));
  }

  if (validation.warnings.length > 0) {
    console.log('\n⚠️ 警告');
    validation.warnings.forEach(warning => console.log(`  - ${warning}`));
  }

  console.log('\n');
}

/**
 * 获取节点实现优先级列表
 */
export function getNodeImplementationPriority(): Array<{ category: string; priority: number; target: number; description: string }> {
  return [
    { category: 'core', priority: 1, target: 14, description: '核心流程控制节点' },
    { category: 'math', priority: 1, target: 16, description: '数学运算节点' },
    { category: 'debug', priority: 1, target: 9, description: '调试工具节点' },
    { category: 'ai', priority: 2, target: 46, description: 'AI智能节点' },
    { category: 'network', priority: 2, target: 43, description: '网络通信节点' },
    { category: 'ui', priority: 3, target: 34, description: 'UI界面节点' },
    { category: 'multimedia', priority: 3, target: 62, description: '多媒体处理节点' },
    { category: 'specialized', priority: 4, target: 119, description: '专业应用节点' }
  ];
}

/**
 * 生成节点实现计划
 */
export function generateNodeImplementationPlan(): string {
  const priorities = getNodeImplementationPriority();
  let plan = '# 视觉脚本节点实现计划\n\n';
  
  plan += '## 优先级说明\n';
  plan += '- 优先级1: 核心基础节点，必须优先实现\n';
  plan += '- 优先级2: 重要功能节点，第二批实现\n';
  plan += '- 优先级3: 增强功能节点，第三批实现\n';
  plan += '- 优先级4: 专业应用节点，最后实现\n\n';
  
  priorities.forEach((item, index) => {
    plan += `## ${index + 1}. ${item.description}\n`;
    plan += `- **类别**: ${item.category}\n`;
    plan += `- **优先级**: ${item.priority}\n`;
    plan += `- **目标数量**: ${item.target}个\n`;
    plan += `- **状态**: ${item.category === 'core' || item.category === 'math' || item.category === 'debug' || item.category === 'ai' ? '✅ 已实现' : '⏳ 待实现'}\n\n`;
  });
  
  return plan;
}

/**
 * 默认导出注册函数
 */
export default registerAllNodes;
